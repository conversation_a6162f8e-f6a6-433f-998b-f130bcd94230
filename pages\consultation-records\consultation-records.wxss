/* pages/consultation-records/consultation-records.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 750rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 记录列表容器 */
.records-container {
  padding: 30rpx;
  width: 750rpx;
  box-sizing: border-box;
  margin-top: -200rpx;
}

/* 记录项 */
.record-item {
  background-color: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 记录头部 */
.record-header {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-id {
  font-size: 32rpx;
  font-weight: bold;
}

.record-time {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 记录内容 */
.record-content {
  padding: 30rpx;
}

.record-row {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.record-row:last-child {
  margin-bottom: 0;
}

.record-label {
  font-size: 30rpx;
  color: #666;
  min-width: 160rpx;
  font-weight: 500;
}

.record-value {
  font-size: 30rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 咨询内容特殊样式 */
.content-row {
  align-items: flex-start;
}

.content-text {
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  border-left: 4rpx solid #FF4444;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500rpx;
  padding: 60rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.5;
}

.empty-btn {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  padding: 20rpx 60rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
}

.empty-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.3);
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500rpx;
  padding: 60rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 36rpx;
  color: #FF4444;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.5;
}

.error-btn {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  padding: 20rpx 60rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
}

.error-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.3);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .record-label {
    min-width: 140rpx;
    font-size: 28rpx;
  }
  
  .record-value {
    font-size: 28rpx;
  }
  
  .record-id {
    font-size: 30rpx;
  }
  
  .record-time {
    font-size: 24rpx;
  }
}
