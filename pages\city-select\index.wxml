<!--pages/city-select/index.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-box">
      <image class="search-icon" src="../../images/search.png"></image>
      <input class="search-input" placeholder="搜索城市名称" bindinput="onSearchInput" value="{{searchValue}}" />
    </view>
  </view>

  <!-- 城市列表 -->
  <view class="city-list-container">
    <!-- 字母索引 -->
    <!-- <view class="alphabet-index"
          bindtouchstart="onAlphabetTouchStart"
          bindtouchmove="onAlphabetTouchMove"
          bindtouchend="onAlphabetTouchEnd">
      <view class="alphabet-item {{currentAlphabet === item ? 'active' : ''}}"
            wx:for="{{alphabetList}}"
            wx:key="*this"
            bindtap="onAlphabetTap"
            data-alphabet="{{item}}"
            data-index="{{index}}">
        {{item}}
      </view>
    </view> -->

    <!-- 城市列表 -->
    <scroll-view class="city-list" scroll-y="true" scroll-into-view="{{scrollIntoView}}" scroll-with-animation="true" scroll-top="{{scrollTop}}">
      <view class="city-group" wx:for="{{filteredCityList}}" wx:key="letter" id="alphabet-{{item.letter}}">
        <view class="group-title">{{item.letter}}</view>
        <view class="city-items">
          <view class="city-item {{selectedCity === city.name ? 'selected' : ''}}"
                wx:for="{{item.cities}}"
                wx:for-item="city"
                wx:key="id"
                bindtap="onCitySelect"
                data-city="{{city.name}}">
            {{city.displayName || city.name}}
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 字母提示 -->
  <view class="alphabet-tip" wx:if="{{showAlphabetTip}}">
    {{currentAlphabet}}
  </view>
</view>
