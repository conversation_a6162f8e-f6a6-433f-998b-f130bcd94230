/* pages/city-select/index.wxss */
.container {
  width: 100%;
  margin-top: -200rpx;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 搜索区域 */
.search-section {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 30rpx;
  padding: 20rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

/* 城市列表容器 */
.city-list-container {
  flex: 1;
  display: flex;
  background-color: #ffffff;
  position: relative;
  width: 100%;
}

/* 字母索引 */
.alphabet-index {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 20rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.alphabet-item {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666666;
  margin: 2rpx 0;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.alphabet-item.active {
  background-color: #F8877D;
  color: #ffffff;
}

/* 城市列表 */
.city-list {
  flex: 1;
  height: 100%;
  padding-right: 20rpx;
  box-sizing: border-box;
}

.city-group {
  margin-bottom: 20rpx;
}

.group-title {
  padding: 20rpx 30rpx 10rpx;
  font-size: 24rpx;
  color: #999999;
  background-color: #f8f8f8;
  position: sticky;
  top: 0;
  z-index: 10;
}

.city-items {
  background-color: #ffffff;
}

.city-item {
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  color: #333333;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.city-item:last-child {
  border-bottom: none;
}

.city-item.selected {
  background-color: #FFF0EF;
  color: #F8877D;
}

.city-item:active {
  background-color: #f8f8f8;
}

/* 字母提示 */
.alphabet-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 60rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
  z-index: 2000;
}
