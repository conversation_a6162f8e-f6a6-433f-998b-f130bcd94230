// pages/settings/settings.js
const userService = require('../../services/user')
const api = require('../../config/api')

Page({
  data: {
    statusBarHeight: 0,
    navBarHeight: 88,
    userInfo: {
      nickname: '微信用户',
      avatarUrl: '',
      phoneNumber: ''
    },
    phoneDisplay: '未绑定',
    // 弹窗显示状态
    showAvatarModal: false,
    showNicknameModal: false,
    showPhoneModal: false,
    // 临时数据
    tempNickname: '',
    tempAvatarUrl: ''
  },

  onLoad(options) {
    this.initNavBar()
    this.loadUserInfo()
  },

  onShow() {
    this.loadUserInfo()
  },

  /**
   * 初始化导航栏
   */
  initNavBar() {
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: systemInfo.statusBarHeight + 88
    })
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const userInfo = userService.getLocalUserInfo() || {}
    const phoneDisplay = userInfo.phoneNumber ? 
      userInfo.phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : 
      '未绑定'
    
    this.setData({
      userInfo: {
        nickname: userInfo.nickname || '微信用户',
        avatarUrl: userInfo.avatarUrl || '',
        phoneNumber: userInfo.phoneNumber || ''
      },
      phoneDisplay: phoneDisplay
    })
  },

  /**
   * 返回按钮点击事件
   */
  onBack() {
    wx.navigateBack()
  },

  /**
   * 头像点击事件
   */
  onAvatarTap() {
    this.setData({
      showAvatarModal: true
    })
  },

  /**
   * 昵称点击事件
   */
  onNicknameTap() {
    this.setData({
      showNicknameModal: true,
      tempNickname: this.data.userInfo.nickname
    })
  },

  /**
   * 手机号点击事件
   */
  onPhoneTap() {
    if (this.data.userInfo.phoneNumber) {
      wx.showToast({
        title: '手机号已绑定',
        icon: 'none'
      })
    } else {
      this.setData({
        showPhoneModal: true
      })
    }
  },

  /**
   * 隐藏头像弹窗
   */
  hideAvatarModal() {
    this.setData({
      showAvatarModal: false
    })
  },

  /**
   * 隐藏昵称弹窗
   */
  hideNicknameModal() {
    this.setData({
      showNicknameModal: false,
      tempNickname: ''
    })
  },

  /**
   * 隐藏手机号弹窗
   */
  hidePhoneModal() {
    this.setData({
      showPhoneModal: false
    })
  },

  /**
   * 阻止弹窗关闭
   */
  preventClose() {
    // 阻止事件冒泡
  },

  /**
   * 选择头像
   */
  onChooseAvatar(e) {
    const avatarUrl = e.detail.avatarUrl
    console.log('选择头像:', avatarUrl)

    this.hideAvatarModal()

    // 上传头像到服务器
    this.uploadAvatar(avatarUrl)
  },

  /**
   * 上传头像到服务器
   */
  uploadAvatar(localPath) {
    wx.showLoading({
      title: '上传头像中...'
    })

    wx.uploadFile({
      url: `${api.baseUrl}/api/cos/upload`,
      filePath: localPath,
      name: 'file',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        wx.hideLoading()

        try {
          const response = JSON.parse(res.data)
          console.log('头像上传响应:', response)

          if (response.code === 200) {
            // 上传成功，更新用户信息
            this.updateUserInfo({
              avatarUrl: response.data.fileUrl
            })

            wx.showToast({
              title: '头像上传成功',
              icon: 'success'
            })
          } else {
            throw new Error(response.message || '头像上传失败')
          }
        } catch (error) {
          console.error('解析上传响应失败:', error)
          wx.showToast({
            title: '头像上传失败',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        wx.hideLoading()
        console.error('头像上传失败:', error)

        wx.showToast({
          title: '头像上传失败，请重试',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 昵称输入
   */
  onNicknameInput(e) {
    this.setData({
      tempNickname: e.detail.value.trim()
    })
  },

  /**
   * 确认昵称修改
   */
  confirmNickname() {
    if (!this.data.tempNickname) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    this.updateUserInfo({
      nickname: this.data.tempNickname
    })
    
    this.hideNicknameModal()
  },

  /**
   * 获取手机号
   */
  onGetPhoneNumber(e) {
    console.log('获取手机号:', e)
    
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      this.handlePhoneNumber(e.detail.code)
    } else {
      wx.showToast({
        title: '需要授权手机号',
        icon: 'none'
      })
    }
    
    this.hidePhoneModal()
  },

  /**
   * 处理手机号获取
   */
  async handlePhoneNumber(code) {
    try {
      wx.showLoading({
        title: '获取手机号中...'
      })

      const response = await api.get(`${api.API.WECHAT_PHONE}?code=${code}`)
      console.log('手机号API完整响应:', response)

      if (response.code === 200) {
        // 详细检查返回的数据结构
        console.log('response.data类型:', typeof response.data)
        console.log('response.data内容:', JSON.stringify(response.data))

        let phoneNumber = null

        if (response.data) {
          // 尝试不同的数据结构
          phoneNumber = response.data.phoneNumber ||
                       response.data.phone_number ||
                       response.data.mobile ||
                       response.data.phone ||
                       response.data.purePhoneNumber ||
                       response.data.countryCode ||
                       (typeof response.data === 'string' ? response.data : null)
        }

        console.log('解析到的手机号:', phoneNumber)

        if (phoneNumber) {
          this.updateUserInfo({
            phoneNumber: phoneNumber
          })

          wx.hideLoading()
          wx.showToast({
            title: '手机号绑定成功',
            icon: 'success'
          })
        } else {
          // 后端API问题，显示详细信息给开发者
          wx.hideLoading()

          wx.showModal({
            title: '后端API问题',
            content: `后端API返回成功但未包含手机号数据。\n\n返回数据：${JSON.stringify(response)}\n\n请联系后端开发者修复 /api/wechat/phone-number 接口`,
            showCancel: true,
            cancelText: '取消',
            confirmText: '模拟成功',
            success: (res) => {
              if (res.confirm) {
                // 开发阶段的临时解决方案：模拟手机号获取成功
                const mockPhoneNumber = '138****' + Math.floor(Math.random() * 10000).toString().padStart(4, '0')

                this.updateUserInfo({
                  phoneNumber: mockPhoneNumber
                })
              }
            }
          })
          return
        }
      } else {
        throw new Error(response.message || '获取手机号失败')
      }
    } catch (error) {
      wx.hideLoading()
      console.error('获取手机号失败:', error)

      // 显示更详细的错误信息
      let errorMessage = '获取手机号失败'
      if (error.message) {
        errorMessage = error.message
      }

      wx.showModal({
        title: '获取手机号失败',
        content: `错误信息：${errorMessage}\n\n请检查网络连接或联系客服`,
        showCancel: false,
        confirmText: '确定'
      })
    }
  },

  /**
   * 更新用户信息
   */
  async updateUserInfo(updateData) {
    try {
      wx.showLoading({
        title: '保存中...'
      })

      const currentUserInfo = userService.getLocalUserInfo() || {}
      
      // 构建完整的用户对象
      const fullUpdateData = {
        id: currentUserInfo.id,
        wechatOpenid: currentUserInfo.wechatOpenid,
        role: currentUserInfo.role || 0,
        createTime: currentUserInfo.createTime,
        favoriteHouseIds: currentUserInfo.favoriteHouseIds || [],
        followedHouseIds: currentUserInfo.followedHouseIds || [],
        nickname: currentUserInfo.nickname,
        avatarUrl: currentUserInfo.avatarUrl,
        phoneNumber: currentUserInfo.phoneNumber,
        ...updateData
      }

      console.log('更新用户信息:', fullUpdateData)

      const response = await api.put(api.API.USER_UPDATE, fullUpdateData)
      
      if (response.code === 200) {
        // 更新本地存储
        const newUserInfo = { ...currentUserInfo, ...updateData }
        userService.saveLocalUserInfo(newUserInfo)
        
        // 更新页面显示
        this.loadUserInfo()
        
        wx.hideLoading()
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
      } else {
        throw new Error(response.message || '保存失败')
      }
    } catch (error) {
      wx.hideLoading()
      console.error('更新用户信息失败:', error)
      
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 退出登录
   */
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '退出',
      confirmColor: '#F8877D',
      success: (res) => {
        if (res.confirm) {
          this.performLogout()
        }
      }
    })
  },

  /**
   * 执行退出登录
   */
  async performLogout() {
    try {
      wx.showLoading({
        title: '退出中...'
      })

      // 调用退出登录服务
      await userService.logout()
      
      wx.hideLoading()
      
      wx.showToast({
        title: '已退出登录',
        icon: 'success',
        duration: 1500
      })

      // 延迟跳转到首页并刷新
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)

    } catch (error) {
      wx.hideLoading()
      console.error('退出登录失败:', error)
      
      wx.showToast({
        title: '退出失败，请重试',
        icon: 'none'
      })
    }
  }
})
