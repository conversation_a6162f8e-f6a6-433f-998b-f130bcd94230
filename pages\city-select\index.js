// pages/city-select/index.js
const cityService = require('../../services/city.js')
const houseService = require('../../services/house.js')

Page({
  data: {
    statusBarHeight: 0,
    navBarHeight: 88,
    searchValue: '',
    selectedCity: '',
    currentAlphabet: 'A',
    showAlphabetTip: false,
    scrollIntoView: '',
    scrollTop: 0,
    alphabetList: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
    cityList: [],
    filteredCityList: []
  },

  onLoad(options) {
    this.initNavBar();
    this.loadSelectedCity();
    this.initCityData();
  },

  /**
   * 初始化导航栏
   */
  initNavBar() {
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: systemInfo.statusBarHeight + 88
    });
  },

  /**
   * 加载选中的城市
   */
  loadSelectedCity() {
    const selectedCity = wx.getStorageSync('selectedCity') || '重庆市区';
    this.setData({
      selectedCity: selectedCity
    });
  },

  /**
   * 初始化城市数据
   */
  async initCityData() {
    try {
      // 调用API获取城市列表
      const response = await houseService.getAllCities()
      console.log('获取到的城市数据:', response)
      
      if (response && response.data && Array.isArray(response.data)) {
        // 处理API返回的城市数据
        const cities = response.data
        
        // 直接使用API返回的城市数据
        const processedCities = cities
        
        // 按拼音首字母分组
        const cityData = this.groupCitiesByPinyin(processedCities)
        
        const cityList = Object.keys(cityData).sort().map(letter => ({
          letter: letter,
          cities: cityData[letter].map((city, index) => ({
            name: city,
            pinyin: this.convertToPinyin(city),
            id: `${letter}-${index}`,
            displayName: cityService.formatCityDisplay(city)
          }))
        }))

        this.setData({
          cityList: cityList,
          filteredCityList: cityList
        })
      } else {
        console.error('API返回数据格式错误:', response)
        this.loadDefaultCityData() // 降级到默认数据
      }
    } catch (error) {
      console.error('获取城市数据失败:', error)
      this.loadDefaultCityData() // 降级到默认数据
    }
  },

  /**
   * 城市按拼音首字母分组
   */
  groupCitiesByPinyin(cities) {
    const grouped = {}
    
    cities.forEach(city => {
      const firstLetter = this.getFirstLetter(city).toUpperCase()
      if (!grouped[firstLetter]) {
        grouped[firstLetter] = []
      }
      grouped[firstLetter].push(city)
    })
    
    return grouped
  },

  /**
   * 获取城市名称的拼音首字母
   */
  getFirstLetter(city) {
    // 简单的拼音首字母映射，可以根据需要扩展
    const pinyinMap = {
      '重': 'C', '庆': 'Q', '北': 'B', '京': 'J', '上': 'S', '海': 'H',
      '广': 'G', '州': 'Z', '深': 'S', '圳': 'Z', '天': 'T', '津': 'J',
      '南': 'N', '宁': 'N', '杭': 'H', '西': 'X', '安': 'A', '成': 'C',
      '都': 'D', '武': 'W', '汉': 'H', '长': 'C', '沙': 'S', '厦': 'X',
      '门': 'M', '青': 'Q', '岛': 'D', '大': 'D', '连': 'L', '沈': 'S',
      '阳': 'Y', '哈': 'H', '尔': 'E', '滨': 'B', '石': 'S', '家': 'J',
      '太': 'T', '原': 'Y', '郑': 'Z', '济': 'J', '南': 'N', '合': 'H',
      '肥': 'F', '福': 'F', '州': 'Z', '昆': 'K', '明': 'M', '乌': 'W',
      '鲁': 'L', '木': 'M', '齐': 'Q', '银': 'Y', '川': 'C', '兰': 'L',
      '拉': 'L', '萨': 'S', '呼': 'H', '和': 'H', '浩': 'H', '特': 'T'
    }
    
    const firstChar = city.charAt(0)
    return pinyinMap[firstChar] || 'A' // 默认归类到A
  },

  /**
   * 简单的拼音转换
   */
  convertToPinyin(city) {
    // 这里只是简单处理，实际项目中可以使用专门的拼音库
    return city.toLowerCase()
  },

  /**
   * 加载默认城市数据（降级方案）
   */
  loadDefaultCityData() {
    // 中国主要城市数据，按拼音首字母分组
    const cityData = {
      'A': [
        { name: '安庆', pinyin: 'anqing' },
        { name: '安阳', pinyin: 'anyang' },
        { name: '鞍山', pinyin: 'anshan' },
        { name: '安康', pinyin: 'ankang' },
        { name: '安顺', pinyin: 'anshun' },
        { name: '阿克苏', pinyin: 'akesu' },
        { name: '阿勒泰', pinyin: 'aletai' }
      ],
      'B': [
        { name: '北京', pinyin: 'beijing' },
        { name: '保定', pinyin: 'baoding' },
        { name: '包头', pinyin: 'baotou' },
        { name: '本溪', pinyin: 'benxi' },
        { name: '蚌埠', pinyin: 'bengbu' },
        { name: '白城', pinyin: 'baicheng' },
        { name: '白山', pinyin: 'baishan' },
        { name: '百色', pinyin: 'baise' },
        { name: '北海', pinyin: 'beihai' },
        { name: '巴中', pinyin: 'bazhong' },
        { name: '毕节', pinyin: 'bijie' },
        { name: '保山', pinyin: 'baoshan' }
      ],
      'C': [
        { name: '重庆市区', pinyin: 'chongqingshiqu' },
        { name: '重庆郊区', pinyin: 'chongqingjiaoqu' },
        { name: '成都', pinyin: 'chengdu' },
        { name: '长沙', pinyin: 'changsha' },
        { name: '长春', pinyin: 'changchun' },
        { name: '常州', pinyin: 'changzhou' },
        { name: '长治', pinyin: 'changzhi' },
        { name: '承德', pinyin: 'chengde' },
        { name: '沧州', pinyin: 'cangzhou' },
        { name: '赤峰', pinyin: 'chifeng' },
        { name: '朝阳', pinyin: 'chaoyang' },
        { name: '长白山', pinyin: 'changbaishan' },
        { name: '滁州', pinyin: 'chuzhou' },
        { name: '池州', pinyin: 'chizhou' },
        { name: '巢湖', pinyin: 'chaohu' },
        { name: '潮州', pinyin: 'chaozhou' },
        { name: '郴州', pinyin: 'chenzhou' },
        { name: '常德', pinyin: 'changde' },
        { name: '崇左', pinyin: 'chongzuo' },
        { name: '昌吉', pinyin: 'changji' }
      ],
      'D': [
        { name: '大连', pinyin: 'dalian' },
        { name: '东莞', pinyin: 'dongguan' },
        { name: '大庆', pinyin: 'daqing' },
        { name: '丹东', pinyin: 'dandong' },
        { name: '大同', pinyin: 'datong' },
        { name: '德州', pinyin: 'dezhou' },
        { name: '东营', pinyin: 'dongying' },
        { name: '德阳', pinyin: 'deyang' },
        { name: '达州', pinyin: 'dazhou' },
        { name: '大理', pinyin: 'dali' },
        { name: '德宏', pinyin: 'dehong' },
        { name: '定西', pinyin: 'dingxi' }
      ],
      'E': [
        { name: '鄂尔多斯', pinyin: 'eerduosi' },
        { name: '恩施', pinyin: 'enshi' },
        { name: '鄂州', pinyin: 'ezhou' }
      ],
      'F': [
        { name: '福州', pinyin: 'fuzhou' },
        { name: '佛山', pinyin: 'foshan' },
        { name: '抚顺', pinyin: 'fushun' },
        { name: '阜新', pinyin: 'fuxin' },
        { name: '阜阳', pinyin: 'fuyang' },
        { name: '抚州', pinyin: 'fuzhou' },
        { name: '防城港', pinyin: 'fangchenggang' }
      ],
      'G': [
        { name: '广州', pinyin: 'guangzhou' },
        { name: '贵阳', pinyin: 'guiyang' },
        { name: '桂林', pinyin: 'guilin' },
        { name: '赣州', pinyin: 'ganzhou' },
        { name: '广元', pinyin: 'guangyuan' },
        { name: '广安', pinyin: 'guangan' },
        { name: '贵港', pinyin: 'guigang' },
        { name: '固原', pinyin: 'guyuan' },
        { name: '甘南', pinyin: 'gannan' },
        { name: '甘孜', pinyin: 'ganzi' }
      ],
      'H': [
        { name: '杭州', pinyin: 'hangzhou' },
        { name: '哈尔滨', pinyin: 'haerbin' },
        { name: '合肥', pinyin: 'hefei' },
        { name: '海口', pinyin: 'haikou' },
        { name: '呼和浩特', pinyin: 'huhehaote' },
        { name: '石家庄', pinyin: 'shijiazhuang' },
        { name: '邯郸', pinyin: 'handan' },
        { name: '衡水', pinyin: 'hengshui' },
        { name: '葫芦岛', pinyin: 'huludao' },
        { name: '鸡西', pinyin: 'jixi' },
        { name: '鹤岗', pinyin: 'hegang' },
        { name: '黑河', pinyin: 'heihe' },
        { name: '淮安', pinyin: 'huaian' },
        { name: '淮南', pinyin: 'huainan' },
        { name: '淮北', pinyin: 'huaibei' },
        { name: '黄山', pinyin: 'huangshan' },
        { name: '惠州', pinyin: 'huizhou' },
        { name: '河源', pinyin: 'heyuan' },
        { name: '汕尾', pinyin: 'shanwei' },
        { name: '湛江', pinyin: 'zhanjiang' },
        { name: '茂名', pinyin: 'maoming' },
        { name: '肇庆', pinyin: 'zhaoqing' },
        { name: '韶关', pinyin: 'shaoguan' },
        { name: '衡阳', pinyin: 'hengyang' },
        { name: '怀化', pinyin: 'huaihua' },
        { name: '湘潭', pinyin: 'xiangtan' },
        { name: '海南', pinyin: 'hainan' },
        { name: '河池', pinyin: 'hechi' },
        { name: '贺州', pinyin: 'hezhou' },
        { name: '海东', pinyin: 'haidong' },
        { name: '海北', pinyin: 'haibei' },
        { name: '黄南', pinyin: 'huangnan' },
        { name: '海西', pinyin: 'haixi' },
        { name: '哈密', pinyin: 'hami' },
        { name: '和田', pinyin: 'hetian' }
      ],
      'J': [
        { name: '济南', pinyin: 'jinan' },
        { name: '金华', pinyin: 'jinhua' },
        { name: '嘉兴', pinyin: 'jiaxing' },
        { name: '江门', pinyin: 'jiangmen' },
        { name: '佳木斯', pinyin: 'jiamusi' },
        { name: '鸡西', pinyin: 'jixi' },
        { name: '九江', pinyin: 'jiujiang' },
        { name: '景德镇', pinyin: 'jingdezhen' },
        { name: '吉安', pinyin: 'jian' },
        { name: '济宁', pinyin: 'jining' },
        { name: '焦作', pinyin: 'jiaozuo' },
        { name: '荆门', pinyin: 'jingmen' },
        { name: '荆州', pinyin: 'jingzhou' },
        { name: '江汉', pinyin: 'jianghan' },
        { name: '晋中', pinyin: 'jinzhong' },
        { name: '晋城', pinyin: 'jincheng' },
        { name: '酒泉', pinyin: 'jiuquan' },
        { name: '嘉峪关', pinyin: 'jiayuguan' },
        { name: '金昌', pinyin: 'jinchang' }
      ],
      'K': [
        { name: '昆明', pinyin: 'kunming' },
        { name: '开封', pinyin: 'kaifeng' },
        { name: '克拉玛依', pinyin: 'kelamayi' },
        { name: '喀什', pinyin: 'kashi' },
        { name: '克孜勒苏', pinyin: 'kezilesu' }
      ],
      'L': [
        { name: '兰州', pinyin: 'lanzhou' },
        { name: '洛阳', pinyin: 'luoyang' },
        { name: '临沂', pinyin: 'linyi' },
        { name: '聊城', pinyin: 'liaocheng' },
        { name: '莱芜', pinyin: 'laiwu' },
        { name: '廊坊', pinyin: 'langfang' },
        { name: '辽阳', pinyin: 'liaoyang' },
        { name: '辽源', pinyin: 'liaoyuan' },
        { name: '连云港', pinyin: 'lianyungang' },
        { name: '丽水', pinyin: 'lishui' },
        { name: '六安', pinyin: 'luan' },
        { name: '娄底', pinyin: 'loudi' },
        { name: '柳州', pinyin: 'liuzhou' },
        { name: '来宾', pinyin: 'laibin' },
        { name: '乐山', pinyin: 'leshan' },
        { name: '泸州', pinyin: 'luzhou' },
        { name: '凉山', pinyin: 'liangshan' },
        { name: '丽江', pinyin: 'lijiang' },
        { name: '临沧', pinyin: 'lincang' },
        { name: '陇南', pinyin: 'longnan' },
        { name: '临夏', pinyin: 'linxia' },
        { name: '林芝', pinyin: 'linzhi' }
      ],
      'M': [
        { name: '绵阳', pinyin: 'mianyang' },
        { name: '马鞍山', pinyin: 'maanshan' },
        { name: '牡丹江', pinyin: 'mudanjiang' },
        { name: '茂名', pinyin: 'maoming' },
        { name: '梅州', pinyin: 'meizhou' },
        { name: '眉山', pinyin: 'meishan' },
        { name: '绵竹', pinyin: 'mianzhu' }
      ],
      'N': [
        { name: '南京', pinyin: 'nanjing' },
        { name: '南昌', pinyin: 'nanchang' },
        { name: '南宁', pinyin: 'nanning' },
        { name: '宁波', pinyin: 'ningbo' },
        { name: '南通', pinyin: 'nantong' },
        { name: '南阳', pinyin: 'nanyang' },
        { name: '内江', pinyin: 'neijiang' },
        { name: '南充', pinyin: 'nanchong' },
        { name: '怒江', pinyin: 'nujiang' },
        { name: '那曲', pinyin: 'naqu' }
      ],
      'P': [
        { name: '盘锦', pinyin: 'panjin' },
        { name: '莆田', pinyin: 'putian' },
        { name: '平顶山', pinyin: 'pingdingshan' },
        { name: '濮阳', pinyin: 'puyang' },
        { name: '萍乡', pinyin: 'pingxiang' },
        { name: '攀枝花', pinyin: 'panzhihua' },
        { name: '普洱', pinyin: 'puer' },
        { name: '平凉', pinyin: 'pingliang' }
      ],
      'Q': [
        { name: '青岛', pinyin: 'qingdao' },
        { name: '泉州', pinyin: 'quanzhou' },
        { name: '秦皇岛', pinyin: 'qinhuangdao' },
        { name: '齐齐哈尔', pinyin: 'qiqihaer' },
        { name: '七台河', pinyin: 'qitaihe' },
        { name: '衢州', pinyin: 'quzhou' },
        { name: '清远', pinyin: 'qingyuan' },
        { name: '钦州', pinyin: 'qinzhou' },
        { name: '黔东南', pinyin: 'qiandongnan' },
        { name: '黔南', pinyin: 'qiannan' },
        { name: '黔西南', pinyin: 'qianxinan' },
        { name: '曲靖', pinyin: 'qujing' },
        { name: '庆阳', pinyin: 'qingyang' }
      ],
      'R': [
        { name: '日照', pinyin: 'rizhao' },
        { name: '日喀则', pinyin: 'rikaze' }
      ],
      'S': [
        { name: '上海', pinyin: 'shanghai' },
        { name: '深圳', pinyin: 'shenzhen' },
        { name: '苏州', pinyin: 'suzhou' },
        { name: '沈阳', pinyin: 'shenyang' },
        { name: '石家庄', pinyin: 'shijiazhuang' },
        { name: '绍兴', pinyin: 'shaoxing' },
        { name: '宿迁', pinyin: 'suqian' },
        { name: '宿州', pinyin: 'suzhou' },
        { name: '汕头', pinyin: 'shantou' },
        { name: '汕尾', pinyin: 'shanwei' },
        { name: '韶关', pinyin: 'shaoguan' },
        { name: '邵阳', pinyin: 'shaoyang' },
        { name: '十堰', pinyin: 'shiyan' },
        { name: '随州', pinyin: 'suizhou' },
        { name: '三亚', pinyin: 'sanya' },
        { name: '三门峡', pinyin: 'sanmenxia' },
        { name: '商丘', pinyin: 'shangqiu' },
        { name: '四平', pinyin: 'siping' },
        { name: '松原', pinyin: 'songyuan' },
        { name: '双鸭山', pinyin: 'shuangyashan' },
        { name: '绥化', pinyin: 'suihua' },
        { name: '上饶', pinyin: 'shangrao' },
        { name: '三明', pinyin: 'sanming' },
        { name: '遂宁', pinyin: 'suining' },
        { name: '山南', pinyin: 'shannan' },
        { name: '商洛', pinyin: 'shangluo' },
        { name: '石嘴山', pinyin: 'shizuishan' },
        { name: '石河子', pinyin: 'shihezi' }
      ],
      'T': [
        { name: '天津', pinyin: 'tianjin' },
        { name: '太原', pinyin: 'taiyuan' },
        { name: '台州', pinyin: 'taizhou' },
        { name: '唐山', pinyin: 'tangshan' },
        { name: '铁岭', pinyin: 'tieling' },
        { name: '通辽', pinyin: 'tongliao' },
        { name: '泰安', pinyin: 'taian' },
        { name: '泰州', pinyin: 'taizhou' },
        { name: '铜陵', pinyin: 'tongling' },
        { name: '铜仁', pinyin: 'tongren' },
        { name: '天水', pinyin: 'tianshui' },
        { name: '吐鲁番', pinyin: 'tulufan' },
        { name: '塔城', pinyin: 'tacheng' },
        { name: '图木舒克', pinyin: 'tumushuke' }
      ],
      'W': [
        { name: '武汉', pinyin: 'wuhan' },
        { name: '无锡', pinyin: 'wuxi' },
        { name: '温州', pinyin: 'wenzhou' },
        { name: '潍坊', pinyin: 'weifang' },
        { name: '威海', pinyin: 'weihai' },
        { name: '芜湖', pinyin: 'wuhu' },
        { name: '梧州', pinyin: 'wuzhou' },
        { name: '乌鲁木齐', pinyin: 'wulumuqi' },
        { name: '乌兰察布', pinyin: 'wulanchabu' },
        { name: '乌海', pinyin: 'wuhai' },
        { name: '武威', pinyin: 'wuwei' },
        { name: '吴忠', pinyin: 'wuzhong' },
        { name: '文山', pinyin: 'wenshan' },
        { name: '渭南', pinyin: 'weinan' }
      ],
      'X': [
        { name: '西安', pinyin: 'xian' },
        { name: '厦门', pinyin: 'xiamen' },
        { name: '徐州', pinyin: 'xuzhou' },
        { name: '邢台', pinyin: 'xingtai' },
        { name: '新乡', pinyin: 'xinxiang' },
        { name: '许昌', pinyin: 'xuchang' },
        { name: '信阳', pinyin: 'xinyang' },
        { name: '襄阳', pinyin: 'xiangyang' },
        { name: '孝感', pinyin: 'xiaogan' },
        { name: '咸宁', pinyin: 'xianning' },
        { name: '湘西', pinyin: 'xiangxi' },
        { name: '新余', pinyin: 'xinyu' },
        { name: '宣城', pinyin: 'xuancheng' },
        { name: '西双版纳', pinyin: 'xishuangbanna' },
        { name: '咸阳', pinyin: 'xianyang' },
        { name: '西宁', pinyin: 'xining' },
        { name: '兴安盟', pinyin: 'xinganmeng' },
        { name: '锡林郭勒', pinyin: 'xilinguole' }
      ],
      'Y': [
        { name: '银川', pinyin: 'yinchuan' },
        { name: '扬州', pinyin: 'yangzhou' },
        { name: '盐城', pinyin: 'yancheng' },
        { name: '烟台', pinyin: 'yantai' },
        { name: '营口', pinyin: 'yingkou' },
        { name: '伊春', pinyin: 'yichun' },
        { name: '鹰潭', pinyin: 'yingtan' },
        { name: '宜春', pinyin: 'yichun' },
        { name: '岳阳', pinyin: 'yueyang' },
        { name: '益阳', pinyin: 'yiyang' },
        { name: '永州', pinyin: 'yongzhou' },
        { name: '阳江', pinyin: 'yangjiang' },
        { name: '云浮', pinyin: 'yunfu' },
        { name: '玉林', pinyin: 'yulin' },
        { name: '宜宾', pinyin: 'yibin' },
        { name: '雅安', pinyin: 'yaan' },
        { name: '宜昌', pinyin: 'yichang' },
        { name: '玉溪', pinyin: 'yuxi' },
        { name: '昭通', pinyin: 'zhaotong' },
        { name: '延安', pinyin: 'yanan' },
        { name: '榆林', pinyin: 'yulin' },
        { name: '运城', pinyin: 'yuncheng' },
        { name: '阳泉', pinyin: 'yangquan' },
        { name: '忻州', pinyin: 'xinzhou' },
        { name: '伊犁', pinyin: 'yili' }
      ],
      'Z': [
        { name: '郑州', pinyin: 'zhengzhou' },
        { name: '珠海', pinyin: 'zhuhai' },
        { name: '中山', pinyin: 'zhongshan' },
        { name: '淄博', pinyin: 'zibo' },
        { name: '枣庄', pinyin: 'zaozhuang' },
        { name: '张家口', pinyin: 'zhangjiakou' },
        { name: '镇江', pinyin: 'zhenjiang' },
        { name: '舟山', pinyin: 'zhoushan' },
        { name: '漳州', pinyin: 'zhangzhou' },
        { name: '株洲', pinyin: 'zhuzhou' },
        { name: '张家界', pinyin: 'zhangjiajie' },
        { name: '湛江', pinyin: 'zhanjiang' },
        { name: '肇庆', pinyin: 'zhaoqing' },
        { name: '中卫', pinyin: 'zhongwei' },
        { name: '周口', pinyin: 'zhoukou' },
        { name: '驻马店', pinyin: 'zhumadian' },
        { name: '资阳', pinyin: 'ziyang' },
        { name: '自贡', pinyin: 'zigong' },
        { name: '遵义', pinyin: 'zunyi' },
        { name: '昭通', pinyin: 'zhaotong' },
        { name: '张掖', pinyin: 'zhangye' }
      ]
    };

    const cityList = Object.keys(cityData).map(letter => ({
      letter: letter,
      cities: cityData[letter].map((city, index) => ({
        ...city,
        id: `${letter}-${index}`, // 添加唯一ID
        displayName: cityService.formatCityDisplay(city.name) // 添加显示名称
      }))
    }));

    this.setData({
      cityList: cityList,
      filteredCityList: cityList
    });
  },

  /**
   * 返回按钮点击事件
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    const searchValue = e.detail.value.toLowerCase();
    this.setData({
      searchValue: searchValue
    });

    if (searchValue.trim() === '') {
      this.setData({
        filteredCityList: this.data.cityList
      });
      return;
    }

    // 过滤城市列表
    const filteredList = this.data.cityList.map(group => ({
      letter: group.letter,
      cities: group.cities.filter(city => {
        const cityName = city.name.toLowerCase()
        const cityPinyin = city.pinyin.toLowerCase()
        
        // 支持搜索原始城市名称、拼音和带"市"后缀的城市名称
        const cityWithSuffix = cityService.formatCityDisplay(city.name).toLowerCase()
        
        return cityName.includes(searchValue) || 
               cityPinyin.includes(searchValue) ||
               cityWithSuffix.includes(searchValue)
      })
    })).filter(group => group.cities.length > 0);

    this.setData({
      filteredCityList: filteredList
    });
  },

  /**
   * 字母索引点击事件
   */
  onAlphabetTap(e) {
    const alphabet = e.currentTarget.dataset.alphabet;
    console.log('点击字母索引:', alphabet);

    this.jumpToAlphabet(alphabet);

    // 隐藏字母提示
    setTimeout(() => {
      this.setData({
        showAlphabetTip: false
      });
    }, 800);
  },

  /**
   * 字母索引触摸开始
   */
  onAlphabetTouchStart(e) {
    this.alphabetTouching = true;
    this.handleAlphabetTouch(e);
  },

  /**
   * 字母索引触摸移动
   */
  onAlphabetTouchMove(e) {
    if (this.alphabetTouching) {
      this.handleAlphabetTouch(e);
    }
  },

  /**
   * 字母索引触摸结束
   */
  onAlphabetTouchEnd(e) {
    this.alphabetTouching = false;
    setTimeout(() => {
      this.setData({
        showAlphabetTip: false
      });
    }, 800);
  },

  /**
   * 处理字母索引触摸
   */
  handleAlphabetTouch(e) {
    const touch = e.touches[0];
    const query = wx.createSelectorQuery();

    query.select('.alphabet-index').boundingClientRect((rect) => {
      if (rect) {
        const itemHeight = rect.height / this.data.alphabetList.length;
        const touchY = touch.clientY - rect.top;
        const index = Math.floor(touchY / itemHeight);

        if (index >= 0 && index < this.data.alphabetList.length) {
          const alphabet = this.data.alphabetList[index];
          this.jumpToAlphabet(alphabet);
        }
      }
    }).exec();
  },

  /**
   * 跳转到指定字母
   */
  jumpToAlphabet(alphabet) {
    console.log('跳转到字母:', alphabet);
    const hasGroup = this.data.filteredCityList.some(group => group.letter === alphabet);
    console.log('存在分组:', hasGroup);

    if (hasGroup) {
      this.setData({
        currentAlphabet: alphabet,
        showAlphabetTip: true
      });

      // 方案1：使用scroll-into-view
      this.setData({
        scrollIntoView: '' // 先清空
      });

      wx.nextTick(() => {
        this.setData({
          scrollIntoView: `alphabet-${alphabet}`
        });
        console.log('设置 scrollIntoView:', `alphabet-${alphabet}`);
      });

      // 方案2：使用wx.pageScrollTo作为备用方案
      setTimeout(() => {
        const query = wx.createSelectorQuery().in(this);
        query.select(`#alphabet-${alphabet}`).boundingClientRect((rect) => {
          if (rect) {
            console.log('元素位置:', rect);
            wx.pageScrollTo({
              scrollTop: rect.top,
              duration: 300
            });
          } else {
            console.log('未找到目标元素');
          }
        }).exec();
      }, 300);
    } else {
      this.setData({
        showAlphabetTip: true,
        currentAlphabet: alphabet
      });
    }
  },

  /**
   * 城市选择事件
   */
  onCitySelect(e) {
    const city = e.currentTarget.dataset.city;
    
    // 保存选中的城市到本地存储
    wx.setStorageSync('selectedCity', city);
    
    // 显示选择成功提示
    wx.showToast({
      title: `已选择${city}`,
      icon: 'success',
      duration: 1500
    });

    // 调用城市房源查询API
    this.queryHousesByCity(city);

    // 延迟返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  /**
   * 根据城市查询房源
   */
  async queryHousesByCity(city) {
    try {
      console.log('根据城市查询房源:', city);
      const response = await houseService.getHousesByCity(city);
      console.log('城市房源查询结果:', response);
      
      // 这里可以根据需要做一些处理，比如预加载数据等
      // 当前主要是为了触发API调用，实际的房源显示会在主页面处理
    } catch (error) {
      console.error('城市房源查询失败:', error);
    }
  }
});
