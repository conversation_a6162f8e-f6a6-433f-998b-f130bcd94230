/* pages/property-detail/property-detail.wxss */
.container {
  width: 750rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 750rpx;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  color: white;
  font-size: 50rpx;
  font-weight: bold;
  line-height: 1;
}

.navbar-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.more-icon, .share-icon {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  margin-top: 200rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 主要内容 */
.main-content {
  width: 750rpx;
}

/* 图片轮播区域 */
.image-section {
  position: relative;
  width: 750rpx;
  height: 500rpx;
  margin-top: -230rpx;
}

.property-swiper {
  width: 100%;
  height: 100%;
}

.property-image {
  width: 100%;
  height: 100%;
}

.image-counter {
  position: absolute;
  bottom: 20rpx;
  right: 30rpx;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-tag {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.edit-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  color: white;
  font-size: 30rpx;
  font-weight: bold;
}

/* 楼盘基本信息 */
.property-infomn {
  flex-direction: column;
  position: relative;
}

.property-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

/* 标签区域 */
.tags-section {
  display: flex;
  gap: 10rpx;
  margin-bottom: 30rpx;
}

.tag {
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  color: white;
}

.tag-discount {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
}

.tag-new {
  background: #52c41a;
}

.tag-transport {
  background: #1890ff;
}

.tag-selected {
  background: #FF9800;
  color: white;
}

.tag-item {
  background: #9C27B0;
  color: white;
}

/* 基本信息区域 */
.info-section {
  margin-bottom: 30rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0 0rpx 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 价格信息区域 */
.price-section {
  margin-bottom: 30rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx 0rpx -20rpx 0;
}

.price-row {
  display: flex;
  justify-content:center;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  align-items: center;
  width: 750rpx;
  margin-left: -30rpx;
  position: relative;
}

.price-r {
  display: flex;
  justify-content:center;
  margin-bottom: 20rpx;
  margin-left: -60rpx;
}

.price-1 {
  display: flex;
  justify-content:center;
  margin-bottom: 20rpx;
  margin-left: -62rpx;
  width: 750rpx;
}
.price-qwq{
  font-size: 32rpx;
  color: #999;
  white-space: nowrap;
  display: flex;
}
.price-item {
  flex: 1;
  text-align: center;
  padding:10rpx;
}

.price-i {
  margin-top: 35rpx;
}

.property-price{
  white-space: nowrap;
  position: absolute;
  top: 95rpx;
  left: 10rpx;
}

.price-label {
  white-space: nowrap;
}
.price-va {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  left: 180rpx;
  top: 10rpx;
  position: absolute;
}

.price-valo{
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  left: 490rpx;
  top: 10rpx;
  position: absolute;
}

.price-i{
  text-align: right;
  display: flex;
  justify-content: space-around; 
  align-items: center; 
  padding: 10rpx;
  margin-left: 70rpx; 
}

.price-val{
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
  font-size: 32rpx;
  position: relative;right: 60rpx;bottom: -10rpx;
}

.price-value {
  margin-left: 10rpx;
  font-size: 32rpx;
  color:red;
  font-weight: bold;
}

.price-value-black {
  margin-left: 10rpx;
  font-size: 32rpx;
  color:black;
  font-weight: bold;
}

.price-va{
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
  font-size: 32rpx;
  text-align: left;
  display: flex;
  align-items: center; 
}


.main-price {
  color: #FF4444;
  font-size: 36rpx;
}
.price-lab{
  font-size: 30rpx;
  color: #999; 
  margin-left: -21rpx;
}

.price-label {
  font-size: 30rpx;
  color: #999;
}

/* 起拍时间 */
.time-icon{
  margin-left: 20rpx;
}
.auction-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  height: 80rpx;
  background-color: #FFE5E5;
}

auction-timeopo {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  height: 80rpx;
  background-color: #FFE5E5;
}

.time-text {
  font-size: 35rpx;
  color: red;
}

/* 提示信息 */
.notice-banner {
  background: #ffffff;
  border-radius: 8rpx;
  padding: 16rpx;
  text-align: center;
  height: 10rpx;
  width: 750rpx;
  justify-content: center;
  display: flex;
  margin-left: -30rpx;
}

.notice-text {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 28rpx;
  justify-content:center;
  align-items: center;
  color: black;
}

/* 拍卖信息 */
.auction-info {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
}
.auction-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 700rpx;
  height: 110rpx;
}

.auction-label {
  font-size: 28rpx;
  color: red;
  display: flex;
  margin-left: 30rpx;
  white-space: nowrap;
}

.auction-value {
  font-size: 28rpx;
  color: red;
  display: flex;
  flex-direction: row
}

.follow-btn {
  background: #cd5c5c;
  color: white;
  font-size: 32rpx;
  border-radius: 20rpx;
  border: none;
  margin-left: 50rpx;
}

/* 开发商信息 */
.developer-info {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.developer-row {
  display: flex;
  margin-bottom: 20rpx;
}

.developer-row:last-child {
  margin-bottom: 0;
}

.developer-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.developer-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500rpx;
  padding: 60rpx;
  margin-top: 200rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 36rpx;
  color: #FF4444;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.5;
}

.error-btn {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  padding: 20rpx 60rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: 120rpx;
  background: white;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  box-sizing: border-box;
  z-index: 999;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.bottom-left {
  display: flex;
  gap: 50rpx;
  align-items: center;
}

.contact-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx ;
  border-radius: 12rpx;
  transition: background-color 0.2s;
  min-width: 80rpx;
  margin-left: -40rpx;
}

.contact-item:active {
  background-color: #f5f5f5;
  transform: scale(0.95);
}

/* 已关注状态 */
.contact-item.followed .contact-text {
  color: #007aff;
}

/* 已收藏状态 */
.contact-item.favorited .contact-text {
  color: #ff3b30;
}

.contact-icon {
  width: 44rpx;
  height: 44rpx;
}

.favorite-icon {
  color: #FF4444;
  font-size: 44rpx;
  line-height: 1;
}

.contact-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.bottom-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-left: 170rpx;
}

.consult-btn {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 20rpx;
  border: none;
  padding: 24rpx 80rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
}

.consult-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.3);
  background: linear-gradient(135deg, #E63939 0%, #FF5555 100%);
}

/* 价格对比区域样式 - 按图片格式 */
.price-comparison {
  margin-top: 30rpx;
  padding: 20rpx 0;
}

.price-row-simple {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25rpx;
  width: 100%;
}

.price-row-simple:last-child {
  margin-bottom: 0;
}

.price-item-simple {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
}

.price-item-simple:first-child {
  padding-left: 0;
  padding-right: 60rpx;
}

.price-item-simple:last-child {
  padding-left: 60rpx;
  padding-right: 0;
}

.price-label-simple {
  font-size: 28rpx;
  color: #999;
  min-width: fit-content;
  white-space: nowrap;
}

.price-value-simple {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-left: 20rpx;
}

/* 专用价格标签定位样式 - 替代内联样式 */
.market-price-label {
  position: absolute;
  left:93rpx; 
  top:20rpx;
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}
.iopo {
  position: absolute;
  font-size: 24rpx;
  color: #999;
  margin-top: -24rpx;
  margin-left: 220rpx;
}

.saving-space-label {
  position: absolute;
  left: 385rpx; top: 20rpx
  /* 拍漏空间标签保持默认位置 */
}

.market-unit-price-label {
  position:absolute;
  left: 70rpx;
  top: 20rpx;
  white-space: nowrap;
}

.start-unit-price-label {
  position:absolute;
  left: 385rpx;
  top: 20rpx;
}

/* 图片详情区域样式 */
.image-detail-section {
  width: 750rpx;
  background: #fff;
  padding: 30rpx;
  box-sizing: border-box;
}

.image-detail-section .section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

/* 户型图样式 */
.floor-plan-container {
  margin-bottom: 40rpx;
}

.floor-plan-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.floor-plan-wrapper {
  position: relative;
  width: 100%;
  background: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.detail-image{
  width: 100%;
  height: 400rpx;
  display: block;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.detail-images-grid{
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.detail-image-item {
  position: relative;
  width: 100%;
  border-radius: 8rpx;
  overflow: hidden;
  background: #fff;
}

.floor-plan-image {
  width: 750rpx;
  height: 500rpx;
  display: block;
  margin-left:-30rpx;
}

.floor-plan-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 30rpx 20rpx 20rpx;
  text-align: center;
}

.floor-plan-overlay-text {
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
}

/* 相关附件样式 */
.attachments-section {
  margin-bottom: 30rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.attachments-list {
  margin-top: 20rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  margin-bottom: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.attachment-item:last-child {
  margin-bottom: 0;
}

.attachment-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.attachment-icon {
  font-size: 20rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.attachment-info {
  flex: 1;
}

.attachment-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.attachment-desc {
  font-size: 24rpx;
  color: #666;
}

.attachment-action {
  font-size: 20rpx;
  color: #007aff;
  font-weight: 500;
  padding: 10rpx 20rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
}

/* 实景图片样式 */
.detail-images-container {
  margin-bottom: 30rpx;
}

.detail-image-single {
  position: relative;
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.detail-image-large {
  width: 100%;
  height: 100%;
  display: block;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .property-title {
    font-size: 30rpx;
  }


  .main-price {
    font-size: 34rpx;
  }

  .section-title {
    font-size: 30rpx;
  }

  .floor-plan-image {
    height: 400rpx;
  }

  .detail-image-large {
    height: 320rpx;
  }

  .map-container {
    height: 320rpx;
  }

  .nearby-btn-text {
    font-size: 26rpx;
  }

  .facility-tag-text {
    font-size: 24rpx;
  }

  .transport-name {
    font-size: 26rpx;
  }

  .property-title {
    font-size: 26rpx;
  }
}

/* 楼盘位置样式 */
.location-section {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: 750rpx;
  margin-left: -45rpx;
}

.map-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  background: #fff;
}

.map-image {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 8rpx;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.map-container:active .map-overlay {
  opacity: 1;
}

.map-overlay-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.location-info {
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  backdrop-filter: blur(10rpx);
}

.location-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.location-address {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 周边功能按钮 */
.nearby-buttons {
  display: flex;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.nearby-btn {
  flex: 1;
  padding: 25rpx 0;
  text-align: center;
  background: #f8f9fa;
  border-right: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.nearby-btn:last-child {
  border-right: none;
}

.nearby-btn.active {
  background: linear-gradient(135deg, #FF4444, #FF6B6B);
}

.nearby-btn-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.nearby-btn.active .nearby-btn-text {
  color: #fff;
  font-weight: 600;
}

/* 配套设施标签 */
.facility-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.facility-tag {
  padding: 15rpx 25rpx;
  background: #f8f9fa;
  border-radius: 25rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.facility-tag.active {
  background: rgba(255, 68, 68, 0.1);
  border-color: #FF4444;
}

.facility-tag-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.facility-tag.active .facility-tag-text {
  color: #FF4444;
  font-weight: 600;
}

/* 交通站点列表 */
.transport-list {
  margin-bottom: 30rpx;
}

.transport-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.transport-item:last-child {
  border-bottom: none;
}

.transport-info {
  flex: 1;
  margin-left: 10rpx;
}

.transport-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.transport-lines {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.transport-distance {
  text-align: right;
  position: relative ;left: -20rpx;
}

.distance-text {
  font-size: 26rpx;
  color: #FF4444;
  font-weight: 600;
}

/* 附近房源推荐 */
.nearby-properties-section {
  margin-top: 20rpx;
}

.nearby-properties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.nearby-properties-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.nearby-properties-more {
  font-size: 26rpx;
  color: #999;
  font-weight: 400;
}

.nearby-property-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 214rpx;
  overflow: hidden;
}

.nearby-property-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
}

/* 图片区域 - 占满整个卡片高度 */
.property-image-container {
  position: relative;
  width: 240rpx;
  height: 100%;
  flex-shrink: 0;
  overflow: hidden;
  margin-top: -10rpx;
}

.property-image {
  width: 100%;
  height: 100%;
  display: block;
}

/* 图片左上角标签 */
.property-image-tag {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: #FF4444;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  z-index: 2;
}

.property-image-tag-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
}

/* 二拍标签 - 正确定位 */
.property-tag {
  position: absolute;
  left: 8rpx;
  top: 8rpx;
  background: #FF4444;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  z-index: 2;
}

.property-tag-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}

/* 图片底部地产名称遮罩 */
.property-name-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(1, 1, 1, 5));
  padding: 20rpx 15rpx 15rpx;
  z-index: 10;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.property-name-text {
  color: #fff !important;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  display: block;


}

/* 信息区域 - 与图片等高 */
.property-info {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20rpx;
}

/* 房源标题行 - 支持标签和文字水平排列 */
.property-title-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
  flex-wrap: wrap;
  gap: 8rpx;
}

/* 内联拍卖标签样式 */
.property-tag-inline {
  background: #FF4444;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
  margin-right: 8rpx;
  margin-top: 2rpx;
}

.property-tag-inline .property-tag-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
  line-height: 1;
}

/* 房源标题文字 - 适配新布局 */
.property-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
  flex: 1;
  min-width: 0;
  word-break: break-all;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 375px) {
  .property-title-row {
    gap: 6rpx;
  }

  .property-tag-inline {
    padding: 3rpx 8rpx;
    margin-right: 6rpx;
  }

  .property-tag-inline .property-tag-text {
    font-size: 18rpx;
  }

  .property-title {
    font-size: 26rpx;
  }
}

/* 顶部房源地址和标签 */
.property-header {
  margin-bottom: 15rpx;
}

.property-address-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.property-info-tag {
  background: #FF4444;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.property-info-tag-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
}

.property-address {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
  flex: 1;
}

/* 房源详细信息 */
.property-details {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.property-subtitle{
position: absolute;
left: 10rpx;
white-space: nowrap;
top: 100rpx;
}
.property-area {
  font-size: 24rpx;
  color: white;
  position: absolute;
  top: -30rpx;
  background: #1890ff;
}

.property-rooms {
  font-size: 24rpx;
  color: white;
  position: absolute;
  top: -30rpx;
  left: 75rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
}

.property-location {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.property-transport {
  font-size: 24rpx;
  color: #666;
}

.property-discount {
  font-size: 24rpx;
  color: #FF4444;
  font-weight: 600;
}

/* 底部价格信息区域 */
.property-price-row {
  display: flex;
  align-items: baseline;
}
.property-market-price{
  white-space: nowrap;
  position: absolute;
  top: 35rpx;
}

.price-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.market-price-value {
  font-size: 24rpx;
  color: black;
  text-decoration: line-through;
  position: absolute;
  left:300rpx; top:-25rpx
}

/* 拍卖信息行 */
.auction-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auction-time {
  font-size: 22rpx;
  color: #666;
}

.auction-status {
  font-size: 22rpx;
  color: #FF4444;
  font-weight: 500;
  position: absolute;
  top: 160rpx;
  left: 300rpx;
}

/* 新增样式 - 拍卖信息区域 */
.auction-section {
  margin-bottom: 30rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0 0rpx 30rpx;
}

.auction-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.auction-row:last-child {
  margin-bottom: 0;
}

.auction-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.auction-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.auction-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 新增样式 - 时间区域 */
.time-row {
  display: flex;
  justify-content: space-between;
}

.time-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.time-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 新增样式 - 图片详情区域优化 */
.detail-image-overlay-text {
  color: white;
  font-size: 24rpx;
}
