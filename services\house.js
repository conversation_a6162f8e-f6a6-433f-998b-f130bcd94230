/**
 * 房源相关API服务
 */
const api = require('../config/api.js')

/**
 * 根据房屋类型查询房源
 * @param {number} houseCategory 房屋类型 (0-住宅，1-商办)
 * @returns {Promise} 房源列表
 */
const getHousesByCategory = (houseCategory) => {
  console.log('调用房屋类型查询API:', houseCategory)
  return api.get(`${api.API.HOUSE_BY_CATEGORY}/${houseCategory}`)
    .then(response => {
      console.log('房屋类型查询API响应:', response)
      return response
    })
    .catch(error => {
      console.error('房屋类型查询API失败:', error)
      throw error
    })
}

/**
 * 根据是否特殊房屋查询房源
 * @param {number} isSpecial 是否特殊房屋 (0-否，1-是)
 * @returns {Promise} 房源列表
 */
const getHousesBySpecial = (isSpecial) => {
  console.log('调用特殊房屋查询API:', isSpecial)
  return api.get(`${api.API.HOUSE_BY_SPECIAL}/${isSpecial}`)
    .then(response => {
      console.log('特殊房屋查询API响应:', response)
      return response
    })
    .catch(error => {
      console.error('特殊房屋查询API失败:', error)
      throw error
    })
}

/**
 * 根据是否精选查询房源
 * @param {number} isSelected 是否精选 (0-否，1-是)
 * @returns {Promise} 房源列表
 */
const getHousesBySelected = (isSelected) => {
  console.log('调用精选房源查询API:', isSelected)
  return api.get(`${api.API.HOUSE_BY_SELECTED}/${isSelected}`)
    .then(response => {
      console.log('精选房源查询API响应:', response)
      return response
    })
    .catch(error => {
      console.error('精选房源查询API失败:', error)
      throw error
    })
}

/**
 * 搜索房源
 * @param {string} keyword 搜索关键词
 * @returns {Promise} 房源列表
 */
const searchHouses = (keyword) => {
  console.log('调用房源搜索API:', keyword)
  return api.get(`${api.API.HOUSE_SEARCH}?keyword=${encodeURIComponent(keyword)}`)
    .then(response => {
      console.log('房源搜索API响应:', response)
      return response
    })
    .catch(error => {
      console.error('房源搜索API失败:', error)
      throw error
    })
}

/**
 * 格式化房源数据
 * @param {Object} house 原始房源数据
 * @returns {Object} 格式化后的房源数据
 */
const formatHouseData = (house) => {
  // 处理图片URL
  const getImageUrl = (imageUrls) => {
    if (!imageUrls) return '/images/default-house.png'
    const images = imageUrls.split(',').filter(img => img.trim())
    return images.length > 0 ? images[0].trim() : '/images/default-house.png'
  }

  // 格式化价格（转换为万元）
  const formatPrice = (price) => {
    if (!price) return '0'
    return (price / 10000).toFixed(0)
  }

  // 格式化状态
  const formatStatus = (house) => {
    const now = new Date()
    const startTime = house.startTime ? new Date(house.startTime) : null
    const endTime = house.endTime ? new Date(house.endTime) : null

    if (!startTime) return '待开拍'
    if (now < startTime) return '即将开拍'
    if (endTime && now > endTime) return '已结束'
    return '拍卖中'
  }

  // 格式化时间
  const formatTime = (timeStr) => {
    if (!timeStr) return ''
    try {
      const date = new Date(timeStr)
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的时间格式:', timeStr)
        return timeStr
      }
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')
      return `${month}-${day} ${hour}:${minute}`
    } catch (error) {
      console.error('时间格式化错误:', error, '原始时间:', timeStr)
      return timeStr
    }
  }

  // 验证createTime解析
  const validateCreateTime = (timeStr) => {
    if (!timeStr) return true
    try {
      const date = new Date(timeStr)
      const isValid = !isNaN(date.getTime())
      if (!isValid) {
        console.warn('createTime解析失败:', timeStr)
      }
      return isValid
    } catch (error) {
      console.error('createTime验证错误:', error, timeStr)
      return false
    }
  }

  // 计算单价
  const calculateUnitPrice = (totalPrice, area) => {
    if (!totalPrice || !area) return 0
    return Math.round((totalPrice * 10000) / area)
  }

  // 格式化单价显示（以千为单位，显示整数）
  const formatUnitPrice = (unitPrice) => {
    if (!unitPrice || unitPrice <= 0) return '0千'
    return Math.round(unitPrice / 1000) + '千'
  }

  // 格式化折扣率
  const formatDiscountRate = (rate) => {
    if (!rate || rate >= 100) return null
    return (rate / 10).toFixed(1) + '折'
  }

  // 格式化拍卖次数状态
  const formatAuctionStatus = (auctionStatus) => {
    switch (auctionStatus) {
      case 1:
        return '一拍'
      case 2:
        return '二拍'
      case 3:
        return '变卖'
      default:
        return null
    }
  }

  // 格式化房屋标题
  const formatHouseTitle = (title) => {
    if (!title) return '房源信息'

    // 移除所有空格
    let formattedTitle = title.replace(/\s+/g, '')

    // 移除拍卖相关字样
    formattedTitle = formattedTitle
      .replace(/(一拍|二拍|变卖)/g, '')
      .replace(/[【】]/g, '');

    return formattedTitle || '房源信息'
  }

  return {
    id: house.id,
    title: formatHouseTitle(house.title),
    startingPrice: formatPrice(house.startingPrice),
    evaluationPrice: formatPrice(house.evaluationPrice),
    buildingArea: house.buildingArea || 0,
    communityName: house.communityName || '',
    location: house.communityName || house.title || '位置信息',
    houseType: house.houseType || '',
    floor: house.floor || '',
    imageUrl: getImageUrl(house.imageUrls),
    status: formatStatus(house),
    auctionTime: formatTime(house.startTime),
    endTime: formatTime(house.endTime),
    unitPrice: calculateUnitPrice(house.startingPrice, house.buildingArea),
    // 起拍单价和市场单价，返回纯数字（元/平米）
    startingUnitPrice: house.startingUnitPrice || calculateUnitPrice(house.startingPrice, house.buildingArea),
    marketUnitPrice: house.marketUnitPrice || calculateUnitPrice(house.evaluationPrice, house.buildingArea),
    tags: house.tags || '',
    isSelected: house.isSelected || 0,
    isSpecial: house.isSpecial || 0,
    houseCategory: house.houseCategory || 0,
    auctionStatus: house.auctionStatus || 0,
    auctionStatusText: formatAuctionStatus(house.auctionStatus),
    auctionTimes: house.auctionTimes || 1,
    deposit: formatPrice(house.deposit),
    priceIncrement: formatPrice(house.priceIncrement),
    discountRate: formatDiscountRate(house.discountRate),
    constructionYear: house.constructionYear || '',
    decoration: house.decoration || 0,
    propertyType: house.propertyType || 0,
    stairsType: house.stairsType || 0,
    latitude: house.latitude || 0,
    longitude: house.longitude || 0,
    originalUrl: house.originalUrl || '',

    // 时间字段保留原始格式，用于排序和筛选
    createTime: house.createTime || '',
    startTime: house.startTime || '',
    publishTime: house.publishTime || house.createTime || '', // 发布时间，用于排序

    // 验证时间字段的有效性
    _createTimeValid: validateCreateTime(house.createTime),

    // 用于排序的原始价格字段（保留原始数值）
    startPrice: house.startingPrice || 0,
    evaluationPriceValue: house.evaluationPrice || 0
  }
}

/**
 * 批量格式化房源数据
 * @param {Array} houses 房源数组
 * @returns {Array} 格式化后的房源数组
 */
const formatHouseList = (houses) => {
  if (!Array.isArray(houses)) return []
  return houses.map(house => formatHouseData(house))
}

/**
 * 获取房源统计数据（支持按城市和时间限制）
 * @param {string} city 城市名称，可选
 * @param {string} createTimeFrom 创建时间起，可选
 * @param {string} createTimeTo 创建时间止，可选
 * @returns {Promise} 统计数据
 */
const getHouseStats = async (city = null, createTimeFrom = null, createTimeTo = null) => {
  try {
    console.log('开始获取房源统计数据', { city, createTimeFrom, createTimeTo })

    // 如果没有城市参数，使用默认城市或返回默认数据
    const currentCity = city || '北京市'

    // 并行调用新的API获取统计数据
    try {
      const [
        todayNewRes,
        ongoingRes,
        upcomingRes,
        transactionAmountRes
      ] = await Promise.allSettled([
        api.get(`${api.API.HOUSE_CITY_TODAY}?city=${encodeURIComponent(currentCity)}`),
        api.get(`${api.API.HOUSE_CITY_ONGOING}?city=${encodeURIComponent(currentCity)}`),
        api.get(`${api.API.HOUSE_CITY_UPCOMING}?city=${encodeURIComponent(currentCity)}`),
        api.post(api.API.TRANSACTION_AMOUNT_PAGE, {
          pageNum: 1,
          pageSize: 1000, // 获取足够多的数据来计算平均值
          remarksKeyword: currentCity // 根据城市筛选
        })
      ])

      // 处理API响应
      const getTotalFromResult = (result) => {
        if (result.status === 'fulfilled' && result.value.code === 200 && result.value.data) {
          return result.value.data.total || 0
        }
        return 0
      }

      // 从分页查询结果计算平均成交金额
      const calculateAverageFromTransactionData = (result) => {
        if (result.status === 'fulfilled' && result.value.code === 200 && result.value.data && result.value.data.records) {
          const records = result.value.data.records
          if (records.length === 0) {
            return 0
          }

          // 计算平均值
          const totalAmount = records.reduce((sum, record) => {
            return sum + (parseFloat(record.amount) || 0)
          }, 0)

          const averageAmount = totalAmount / records.length
          return Math.floor(averageAmount) || 0 // 直接返回原始金额（元），不转换为万元
        }
        return 0
      }

      const todayNew = getTotalFromResult(todayNewRes)
      const auctioning = getTotalFromResult(ongoingRes)
      const upcoming = getTotalFromResult(upcomingRes)
      const avgPrice = calculateAverageFromTransactionData(transactionAmountRes) || 7650000 // 默认765万元（以元为单位）

      const stats = {
        todayNew,
        auctioning,
        upcoming,
        avgPrice,
        totalCount: todayNew + auctioning + upcoming,
        city: currentCity
      }

      console.log('新API获取统计数据成功:', stats)
      return stats

    } catch (apiError) {
      console.warn('新API调用失败，使用备用方案:', apiError)
    }

    // 备用方案：并行调用多个API获取统计数据
    const [
      residentialRes,
      commercialRes,
      specialRes,
      selectedRes
    ] = await Promise.allSettled([
      getHousesByCategory(0), // 住宅
      getHousesByCategory(1), // 商办
      getHousesBySpecial(1), // 特殊资产
      getHousesBySelected(1) // 精选房源
    ])

    // 处理结果
    const getDataFromResult = (result) => {
      if (result.status === 'fulfilled' && result.value.code === 200) {
        return Array.isArray(result.value.data) ? result.value.data : []
      }
      return []
    }

    const residential = getDataFromResult(residentialRes)
    const commercial = getDataFromResult(commercialRes)
    const special = getDataFromResult(specialRes)
    const selected = getDataFromResult(selectedRes)

    // 合并所有房源
    const allHouses = [...residential, ...commercial, ...special, ...selected]

    // 计算统计数据
    const now = new Date()

    // 今日新增（模拟）
    const todayNew = Math.floor(Math.random() * 20) + 10

    // 拍卖中的房源
    const auctioning = allHouses.filter(house => {
      if (!house.startTime || !house.endTime) return false
      const startTime = new Date(house.startTime)
      const endTime = new Date(house.endTime)
      return now >= startTime && now <= endTime
    }).length

    // 即将拍卖的房源
    const upcoming = allHouses.filter(house => {
      if (!house.startTime) return false
      const startTime = new Date(house.startTime)
      return now < startTime
    }).length

    // 平均价格（起拍价）
    const validPrices = allHouses.filter(house => house.startingPrice && house.startingPrice > 0)
    const totalPrice = validPrices.reduce((sum, house) => sum + house.startingPrice, 0)
    const avgPrice = validPrices.length > 0 ? Math.floor(totalPrice / validPrices.length / 10000) : 7650

    const stats = {
      todayNew,
      auctioning: auctioning || 116,
      upcoming: upcoming || 2068,
      avgPrice: avgPrice || 7650,
      totalCount: allHouses.length
    }

    console.log('房源统计数据:', stats)
    return stats

  } catch (error) {
    console.error('获取房源统计数据失败:', error)
    // 返回默认统计数据
    return {
      todayNew: Math.floor(Math.random() * 100) + 50,
      auctioning: Math.floor(Math.random() * 200) + 100,
      upcoming: Math.floor(Math.random() * 1000) + 2000,
      avgPrice: Math.floor(Math.random() * 2000) + 7000,
      totalCount: 0
    }
  }
}

/**
 * 解析和格式化时间字符串
 * @param {string} timeStr 时间字符串，如 "2025-01-15 10:00:00"
 * @returns {Object} 包含原始时间、解析后的Date对象和格式化字符串
 */
const parseTimeString = (timeStr) => {
  const result = {
    original: timeStr,
    date: null,
    formatted: '',
    isValid: false
  }

  if (!timeStr) return result

  try {
    const date = new Date(timeStr)
    result.date = date
    result.isValid = !isNaN(date.getTime())

    if (result.isValid) {
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')
      result.formatted = `${month}-${day} ${hour}:${minute}`
    } else {
      console.warn('时间解析失败:', timeStr)
      result.formatted = timeStr
    }
  } catch (error) {
    console.error('时间解析错误:', error, timeStr)
    result.formatted = timeStr
  }

  return result
}

/**
 * 检查createTime字段是否可以正确解析
 * @param {string} createTime 创建时间字符串
 * @returns {boolean} 是否可以正确解析
 */
const validateCreateTime = (createTime) => {
  const parsed = parseTimeString(createTime)
  return parsed.isValid
}

/**
 * 获取所有城市
 * @returns {Promise} 城市列表
 */
const getAllCities = () => {
  console.log('调用获取所有城市API')
  return api.get(api.API.HOUSE_CITIES)
    .then(response => {
      console.log('获取所有城市API响应:', response)
      return response
    })
    .catch(error => {
      console.error('获取所有城市API失败:', error)
      throw error
    })
}

/**
 * 根据城市获取区域
 * @param {string} city 城市名称
 * @returns {Promise} 区域列表
 */
const getDistrictsByCity = (city) => {
  console.log('调用根据城市获取区域API:', city)
  return api.get(`${api.API.HOUSE_DISTRICTS_BY_CITY}/${encodeURIComponent(city)}`)
    .then(response => {
      console.log('根据城市获取区域API响应:', response)
      return response
    })
    .catch(error => {
      console.error('根据城市获取区域API失败:', error)
      throw error
    })
}

/**
 * 根据城市查询房源
 * @param {string} city 城市名称
 * @returns {Promise} 房源列表
 */
const getHousesByCity = (city) => {
  console.log('调用根据城市查询房源API:', city)
  return api.get(`${api.API.HOUSE_BY_CITY}/${encodeURIComponent(city)}`)
    .then(response => {
      console.log('根据城市查询房源API响应:', response)
      return response
    })
    .catch(error => {
      console.error('根据城市查询房源API失败:', error)
      throw error
    })
}

/**
 * 根据区域查询房源
 * @param {string} district 区域名称
 * @returns {Promise} 房源列表
 */
const getHousesByDistrict = (district) => {
  console.log('调用根据区域查询房源API:', district)
  return api.get(`${api.API.HOUSE_BY_DISTRICT}/${encodeURIComponent(district)}`)
    .then(response => {
      console.log('根据区域查询房源API响应:', response)
      return response
    })
    .catch(error => {
      console.error('根据区域查询房源API失败:', error)
      throw error
    })
}

module.exports = {
  getHousesByCategory,
  getHousesBySpecial,
  getHousesBySelected,
  searchHouses,
  formatHouseData,
  formatHouseList,
  getHouseStats,
  parseTimeString,
  validateCreateTime,
  getAllCities,
  getDistrictsByCity,
  getHousesByCity,
  getHousesByDistrict
}