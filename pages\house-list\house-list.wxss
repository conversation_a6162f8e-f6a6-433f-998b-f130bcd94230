/* pages/house-list/house-list.wxss */
.container {
  background-color: #f5f5f5;
}

/* 筛选栏样式 */
.filter-bar {
  margin-top: -180rpx;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #f5f5f5;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  position: relative;
  transition: all 0.2s ease;
}

.filter-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 40rpx;
  background-color: #f5f5f5;
}

.filter-item:active {
  background-color: #f8f8f8;
}

.filter-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-right: 8rpx;
}

.filter-arrow {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
  transition: transform 0.3s ease;
}

/* 多选勾选标记样式 */
.check-mark {
  color: #007AFF;
  font-weight: bold;
  margin-left: 8rpx;
  font-size: 24rpx;
}



/* 房源列表 */
.house-list {
  height: calc(100vh - 200rpx);
  padding: 20rpx 30rpx;
}

.house-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: row;
}

.house-item:active {
  transform: scale(0.98);
}

/* 左侧房源图片区域 */
.house-image-container {
  position: relative;
  width: 260rpx;
  height: 200rpx;
  flex-shrink: 0;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.house-image {
  width: 100%;
  height: 100%;
}

.status-tag {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background-color: #E53E3E;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-weight: bold;
}

/* 小区名称遮罩层 */
.community-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 20rpx 8rpx 8rpx 8rpx;
  display: flex;
  align-items: flex-end;
}

.community-name {
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0 auto 0;
}

/* 右侧房源信息区域 */
.house-info {
  flex: 1;
  padding: 20rpx 20rpx 20rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.title-row {
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.title-row::after {
  content: "";
  display: table;
  clear: both;
}

/* 拍卖状态标签 */
.auction-status-tag {
  background-color: #E53E3E;
  color: #ffffff;
  font-size: 24rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-weight: bold;
  display: inline-block;
  float: left;
  margin-right: 8rpx;
  margin-bottom: 2rpx;
  line-height: 1.2;
}

.house-title-wrapper {
  display: block;
}

.house-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  display: inline;
}

.favorite-btn {
  width: 44rpx;
  height: 44rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.favorite-icon {
  width: 24rpx;
  height: 24rpx;
}

.house-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
  margin-bottom: 8rpx;
}

.house-tag {
  background-color: #FFF5F5;
  color: #E53E3E;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid #FED7D7;
}

/* 房源详情样式 */
.house-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin: 8rpx 0;
}

.detail-item {
  font-size: 24rpx;
  color: #666;
  background-color: #f8f8f8;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.detail-item-discountRate {
  font-size: 20rpx;
  color: rgb(255, 255, 255);
  background-color: #f53333;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.detail-item-area {
  font-size: 20rpx;
  color: rgb(43, 154, 245);
  background-color: #ffffff;
  border: 1px solid blue;
  padding: 1rpx 6rpx;
  border-radius: 4rpx;
}

.info-row {
  margin-bottom: 8rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.location-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}

.location-text {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 8rpx;
}

.price-main {
  display: flex;
  align-items: baseline;
}

.price-label {
  font-size: 22rpx;
  color: #666;
  margin-right: 6rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #E53E3E;
}

.price-unit {
  font-size: 22rpx;
  color: #E53E3E;
  margin-left: 2rpx;
}

.price-market {
  display: flex;
  align-items: baseline;
}

.market-label {
  font-size: 20rpx;
  color: #999;
  margin-right: 6rpx;
}

.market-value {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

.auction-time {
  display: flex;
  align-items: center;
}

.time-icon {
  width: 20rpx;
  height: 20rpx;
  margin-right: 6rpx;
}

.time-text {
  font-size: 22rpx;
  color: #666;
}

/* 加载状态 */
.load-more {
  padding: 40rpx;
  text-align: center;
}

.load-text {
  font-size: 28rpx;
  color: #999;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  padding: 40rpx;
  text-align: center;
}

.no-more-text {
  font-size: 28rpx;
  color: #ccc;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
  margin-top: -1000rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.refresh-btn {
  background-color: #E53E3E;
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}



/* 筛选面板样式 */
.filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  flex-direction: column;
}

.filter-panel-content {
  background-color: #ffffff;
  margin-top: auto;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 70vh;
  overflow-y: auto;
  padding: 40rpx;
}

.filter-options-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.filter-option {
  padding: 20rpx 24rpx;
  background-color: #f8f8f8;
  border-radius: 30rpx;
  text-align: center;
  font-size: 26rpx;
  color: #666666;
  transition: all 0.2s ease;
}

.filter-option.active {
  background-color: #F8877D;
  color: #ffffff;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.custom-price-section {
  margin-bottom: 40rpx;
}

.custom-price-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.custom-price-inputs {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.price-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  font-size: 26rpx;
  text-align: center;
}

.price-unit {
  font-size: 26rpx;
  color: #666666;
}

.price-separator {
  font-size: 26rpx;
  color: #666666;
  margin: 0 10rpx;
}

.sort-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.sort-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option.active .sort-text {
  color: #F8877D;
  font-weight: bold;
}

.sort-text {
  font-size: 28rpx;
  color: #333333;
}

.filter-panel-footer {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.filter-reset-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 40rpx;
}

.reset-icon {
  width: 40rpx;
  height: 40rpx;
}

.filter-confirm-btn {
  flex: 1;
  padding: 24rpx 0;
  background-color: #F8877D;
  color: #ffffff;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}
