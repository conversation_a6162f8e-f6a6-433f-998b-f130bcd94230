<!--pages/settings/settings.wxml-->
<view class="container">

  <!-- 设置内容 -->
  <view class="settings-content">
    <!-- 头像设置 -->
    <view class="setting-item" bindtap="onAvatarTap">
      <view class="setting-left">
        <text class="setting-label">头像</text>
      </view>
      <view class="setting-right">
        <image class="avatar-preview" src="{{userInfo.avatarUrl || '../../images/default-avatar.png'}}" mode="aspectFill"></image>
      </view>
    </view>

    <!-- 昵称设置 -->
    <view class="setting-item" bindtap="onNicknameTap">
      <view class="setting-left">
        <text class="setting-label">昵称</text>
      </view>
      <view class="setting-right">
        <text class="setting-value">{{userInfo.nickname || '未设置'}}</text>
      </view>
    </view>

    <!-- 手机号设置 -->
    <view class="setting-item" bindtap="onPhoneTap">
      <view class="setting-left">
        <text class="setting-label">手机号</text>
      </view>
      <view class="setting-right">
        <text class="setting-value">{{phoneDisplay}}</text>
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>

  <!-- 头像选择弹窗 -->
  <view class="avatar-modal" wx:if="{{showAvatarModal}}" bindtap="hideAvatarModal">
    <view class="avatar-modal-content" catchtap="preventClose">
      <view class="avatar-modal-header">
        <text class="avatar-modal-title">更换头像</text>
        <view class="avatar-modal-close" bindtap="hideAvatarModal">×</view>
      </view>
      
      <view class="avatar-modal-body">
        <button class="avatar-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
          <text class="avatar-btn-text">选择头像</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 昵称修改弹窗 -->
  <view class="nickname-modal" wx:if="{{showNicknameModal}}" bindtap="hideNicknameModal">
    <view class="nickname-modal-content" catchtap="preventClose">
      <view class="nickname-modal-header">
        <text class="nickname-modal-title">修改昵称</text>
        <view class="nickname-modal-close" bindtap="hideNicknameModal">×</view>
      </view>
      
      <view class="nickname-modal-body">
        <input class="nickname-input" 
               type="nickname" 
               placeholder="请输入昵称" 
               bindinput="onNicknameInput" 
               value="{{tempNickname}}" 
               maxlength="20" />
      </view>
      
      <view class="nickname-modal-footer">
        <view class="nickname-cancel-btn" bindtap="hideNicknameModal">取消</view>
        <view class="nickname-confirm-btn {{tempNickname.length > 0 ? 'active' : ''}}" bindtap="confirmNickname">确定</view>
      </view>
    </view>
  </view>

  <!-- 手机号授权弹窗 -->
  <view class="phone-modal" wx:if="{{showPhoneModal}}" bindtap="hidePhoneModal">
    <view class="phone-modal-content" catchtap="preventClose">
      <view class="phone-modal-header">
        <text class="phone-modal-title">绑定手机号</text>
        <view class="phone-modal-close" bindtap="hidePhoneModal">×</view>
      </view>
      
      <view class="phone-modal-body">
        <view class="phone-tip">
          <text class="phone-tip-text">绑定手机号后，可以接收重要通知和找回账号</text>
        </view>
        <button class="phone-btn" open-type="getPhoneNumber" bindgetphonenumber="onGetPhoneNumber">
          <text class="phone-btn-text">授权手机号</text>
        </button>
      </view>
    </view>
  </view>
</view>
