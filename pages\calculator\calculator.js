// pages/calculator/calculator.js
const api = require('../../config/api.js');

Page({
  data: {
    currentTab: 0, // 当前选中的选项卡
    loanAmount: '', // 贷款金额（按贷款总额模式）
    loanYears: 30, // 贷款年限
    loanRate: 3.6, // 贷款利率（默认最新LPR，30年对应3.6%）
    commercialRate: 4.8, // 商贷利率，可手动修改
    
    // API获取的利率数据
    mortgageRatesData: null,
    lpr1year: 0,
    lpr5year: 0,
    updateTime: '',

    // 按房屋总价模式相关
    housePrice: '', // 房屋总价
    loanRatio: '7', // 贷款比例（成）
    calculatedLoanAmount: '', // 计算出的贷款金额

    // 选择器相关
    showYearPicker: false,
    showRateTypePicker: false,
    showRatePicker: false,


    // 年限选项
    yearOptions: ['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年',
                  '11年', '12年', '13年', '14年', '15年', '16年', '17年', '18年', '19年', '20年',
                  '21年', '22年', '23年', '24年', '25年', '26年', '27年', '28年', '29年', '30年'],
    yearIndex: 29, // 默认选中30年

    // 利率类型选项
    rateTypeOptions: ['按最新LPR', '按旧版基准利率'],
    rateTypeIndex: 0,

    // 利率选项（默认为5-30年配置）
    rateOptions: ['3.6%（最新LPR）', '3.2%（首套利率）', '3.2%（二套利率）'],
    rateIndex: 0 // 默认选中第一个选项


  },

  /**
   * 根据贷款年限获取对应的LPR利率配置
   * @param {number} years 贷款年限
   * @returns {object} 包含利率选项和数值的配置对象
   */
  getRateConfigByYears: function(years) {
    const lpr1year = parseFloat((this.data.lpr1year * 100).toFixed(2)); // 转换为百分比并保留2位小数
    const lpr5year = parseFloat((this.data.lpr5year * 100).toFixed(2)); // 转换为百分比并保留2位小数
    
    if (years >= 1 && years <= 4) {
      // 1-4年利率配置
      return {
        rateOptions: [`${lpr1year}%（最新LPR）`, '2.7%（首套利率）', '2.7%（二套利率）'],
        rates: [lpr1year, 2.7, 2.7],
        defaultRate: lpr1year
      };
    } else if (years >= 5 && years <= 30) {
      // 5-30年利率配置
      return {
        rateOptions: [`${lpr5year}%（最新LPR）`, '3.2%（首套利率）', '3.2%（二套利率）'],
        rates: [lpr5year, 3.2, 3.2],
        defaultRate: lpr5year
      };
    } else {
      // 默认配置（异常情况）
      return {
        rateOptions: [`${lpr5year}%（最新LPR）`, '3.2%（首套利率）', '3.2%（二套利率）'],
        rates: [lpr5year, 3.2, 3.2],
        defaultRate: lpr5year
      };
    }
  },

  /**
   * 获取旧版基准利率配置
   * @returns {object} 包含13个旧版基准利率选项和数值的配置对象
   */
  getOldBaseRateConfig: function() {
    const baseRate = 4.35; // 旧版基准利率
    return {
      rateOptions: [
        '旧版基准利率（4.35%）',
        '旧版基准利率7折（3.05%）',
        '旧版基准利率7.5折（3.26%）',
        '旧版基准利率8折（3.48%）',
        '旧版基准利率8.5折（3.7%）',
        '旧版基准利率9折（3.92%）',
        '旧版基准利率9.5折（4.13%）',
        '旧版基准利率1.05倍（4.57%）',
        '旧版基准利率1.1倍（4.79%）',
        '旧版基准利率1.15倍（5%）',
        '旧版基准利率1.2倍（5.22%）',
        '旧版基准利率1.25倍（5.44%）',
        '旧版基准利率1.3倍（5.66%）'
      ],
      rates: [
        4.35,  // 基准利率
        3.05,  // 7折
        3.26,  // 7.5折
        3.48,  // 8折
        3.7,   // 8.5折
        3.92,  // 9折
        4.13,  // 9.5折
        4.57,  // 1.05倍
        4.79,  // 1.1倍
        5.0,   // 1.15倍
        5.22,  // 1.2倍
        5.44,  // 1.25倍
        5.66   // 1.3倍
      ],
      defaultRate: 4.35
    };
  },

  /**
   * 更新利率选项配置
   * @param {number} years 贷款年限（可选，仅在LPR模式下使用）
   */
  updateRateOptions: function(years) {
    const rateTypeIndex = this.data.rateTypeIndex;
    let rateConfig;

    if (rateTypeIndex === 0) {
      // 按最新LPR模式
      const currentYears = years || this.data.loanYears;
      rateConfig = this.getRateConfigByYears(currentYears);
      console.log('更新LPR利率配置 - 年限:', currentYears);
    } else {
      // 按旧版基准利率模式
      rateConfig = this.getOldBaseRateConfig();
      console.log('更新旧版基准利率配置');
    }

    const currentRateIndex = this.data.rateIndex;

    // 保持当前选中的利率索引，如果索引有效的话
    let newRateIndex = 0;
    let newLoanRate = rateConfig.defaultRate;

    if (currentRateIndex >= 0 && currentRateIndex < rateConfig.rates.length) {
      newRateIndex = currentRateIndex;
      newLoanRate = rateConfig.rates[currentRateIndex];
    }

    console.log('新利率选项数量:', rateConfig.rateOptions.length);
    console.log('保持利率索引:', newRateIndex, '新利率值:', newLoanRate);

    this.setData({
      rateOptions: rateConfig.rateOptions,
      rateIndex: newRateIndex,
      loanRate: newLoanRate
    });
  },

  onLoad: function (options) {
    // 页面加载时的逻辑
    console.log('房贷计算页面加载');
    console.log('初始currentTab状态:', this.data.currentTab);

    // 强制重新设置初始状态，确保数据类型正确
    this.setData({
      currentTab: 0,
      loanAmount: '',
      housePrice: '',
      loanRatio: 7,
      calculatedLoanAmount: '',
      // 确保年限选择器的初始状态正确
      loanYears: 30,
      yearIndex: 29  // 30年对应索引29
    });

    console.log('设置后currentTab状态:', this.data.currentTab);
    console.log('设置后年限状态:', this.data.loanYears, '索引:', this.data.yearIndex);
    
    // 加载最新利率数据
    this.loadMortgageRates();
  },

  onReady: function() {
    // 页面渲染完成后再次确认状态
    console.log('页面渲染完成，currentTab:', this.data.currentTab);
  },

  // 返回上一页或首页
  goBack: function() {
    // 获取页面栈
    const pages = getCurrentPages();

    // 如果页面栈中只有当前页面，说明是直接进入的，返回首页
    if (pages.length <= 1) {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    } else {
      // 否则返回上一页
      wx.navigateBack();
    }
  },

  // 切换选项卡
  switchTab: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    console.log('切换选项卡到:', index);
    this.setData({
      currentTab: index
    });
    console.log('当前选项卡状态:', this.data.currentTab);
  },

  // 贷款金额输入（按贷款总额模式）
  onLoanAmountInput: function(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      loanAmount: value
    });
  },

  // 验证数字输入（支持小数点）
  validateNumberInput: function(value) {
    // 只允许数字和一个小数点
    let cleanValue = value.replace(/[^\d.]/g, '');

    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }

    // 限制小数点后最多2位
    if (parts.length === 2 && parts[1].length > 2) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
    }

    return cleanValue;
  },

  // 房屋总价输入
  onHousePriceInput: function(e) {
    const housePrice = this.validateNumberInput(e.detail.value);
    this.setData({
      housePrice: housePrice
    });
    // 自动计算贷款金额
    this.calculateLoanAmount();
  },

  // 计算出的贷款金额输入（手动修改）
  onCalculatedLoanAmountInput: function(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      calculatedLoanAmount: value
    });
  },

  // 贷款比例输入
  onLoanRatioInput: function(e) {
    const value = this.validateLoanRatioInput(e.detail.value);
    this.setData({
      loanRatio: value
    });
    // 重新计算贷款金额
    this.calculateLoanAmount();
  },

  // 验证贷款比例输入
  validateLoanRatioInput: function(value) {
    // 只允许数字和一个小数点
    let cleanValue = value.replace(/[^\d.]/g, '');

    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }

    // 限制小数点后最多1位
    if (parts.length === 2 && parts[1].length > 1) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 1);
    }

    // 限制范围在0.1-9.9之间
    const numValue = parseFloat(cleanValue);
    if (numValue > 9.9) {
      cleanValue = '9.9';
    } else if (numValue < 0.1 && cleanValue !== '' && cleanValue !== '0' && cleanValue !== '0.') {
      cleanValue = '0.1';
    }

    return cleanValue;
  },

  // 自动计算贷款金额
  calculateLoanAmount: function() {
    const housePrice = parseFloat(this.data.housePrice);
    const loanRatio = parseFloat(this.data.loanRatio);

    if (housePrice && loanRatio) {
      const calculatedAmount = (housePrice * loanRatio / 10).toFixed(1);
      this.setData({
        calculatedLoanAmount: calculatedAmount
      });
    }
  },

  // 显示年限选择器
  showYearPicker: function() {
    console.log('显示年限选择器');
    this.setData({
      showYearPicker: true
    });
    console.log('年限选择器状态:', this.data.showYearPicker);
  },

  // 隐藏年限选择器
  hideYearPicker: function() {
    this.setData({
      showYearPicker: false
    });
  },

  // 年限选择变化
  onYearChange: function(e) {
    const index = parseInt(e.detail.value);
    const years = index + 1; // 1年对应index 0，30年对应index 29
    console.log('年限选择变化 - 索引:', index, '年限:', years);
    console.log('年限选项:', this.data.yearOptions[index]);

    // 验证索引范围
    if (index >= 0 && index < this.data.yearOptions.length) {
      this.setData({
        yearIndex: index,
        loanYears: years
      });
      console.log('年限更新成功 - 当前年限:', this.data.loanYears, '期数:', this.data.loanYears * 12);

      // 根据新的年限更新利率选项
      this.updateRateOptions(years);
    } else {
      console.error('年限选择索引超出范围:', index);
    }
  },

  // 显示利率类型选择器
  showRateTypePicker: function() {
    console.log('显示利率类型选择器');
    this.setData({
      showRateTypePicker: true
    });
    console.log('利率类型选择器状态:', this.data.showRateTypePicker);
  },

  // 隐藏利率类型选择器
  hideRateTypePicker: function() {
    this.setData({
      showRateTypePicker: false
    });
  },

  // 利率类型选择变化
  onRateTypeChange: function(e) {
    const index = parseInt(e.detail.value);
    console.log('利率类型选择变化 - 索引:', index, '类型:', this.data.rateTypeOptions[index]);

    // 验证索引范围
    if (index >= 0 && index < this.data.rateTypeOptions.length) {
      this.setData({
        rateTypeIndex: index
      });

      // 根据新的利率类型更新利率选项
      this.updateRateOptions();

      console.log('利率类型更新成功 - 当前类型:', this.data.rateTypeOptions[index]);
    } else {
      console.error('利率类型选择索引超出范围:', index);
    }
  },

  // 显示利率选择器
  showRatePicker: function() {
    console.log('显示利率选择器');
    this.setData({
      showRatePicker: true
    });
    console.log('利率选择器状态:', this.data.showRatePicker);
  },

  // 隐藏利率选择器
  hideRatePicker: function() {
    this.setData({
      showRatePicker: false
    });
  },

  // 利率选择变化
  onRateChange: function(e) {
    const index = parseInt(e.detail.value);
    const rateTypeIndex = this.data.rateTypeIndex;
    let rateConfig;

    // 根据利率类型获取对应的利率配置
    if (rateTypeIndex === 0) {
      // 按最新LPR模式
      rateConfig = this.getRateConfigByYears(this.data.loanYears);
      console.log('利率选择变化 - LPR模式，索引:', index, '年限:', this.data.loanYears);
    } else {
      // 按旧版基准利率模式
      rateConfig = this.getOldBaseRateConfig();
      console.log('利率选择变化 - 旧版基准利率模式，索引:', index);
    }

    const rates = rateConfig.rates;
    console.log('选择利率:', rates[index], '选项:', this.data.rateOptions[index]);

    // 验证索引范围
    if (index >= 0 && index < rates.length) {
      this.setData({
        rateIndex: index,
        loanRate: rates[index]
      });
      console.log('利率更新成功 - 当前利率:', this.data.loanRate + '%');
    } else {
      console.error('利率选择索引超出范围:', index, '最大索引:', rates.length - 1);
    }
  },



  // 开始计算
  calculate: function() {
    let loanAmount = 0;

    // 根据当前选项卡确定贷款金额
    if (this.data.currentTab === 0) {
      // 按贷款总额模式
      if (!this.data.loanAmount) {
        wx.showToast({
          title: '请输入贷款金额',
          icon: 'none'
        });
        return;
      }
      loanAmount = parseFloat(this.data.loanAmount);
    } else {
      // 按房屋总价模式
      if (!this.data.housePrice) {
        wx.showToast({
          title: '请输入房屋总价',
          icon: 'none'
        });
        return;
      }

      // 使用计算出的贷款金额或手动输入的贷款金额
      const calculatedAmount = this.data.calculatedLoanAmount || (parseFloat(this.data.housePrice) * parseFloat(this.data.loanRatio) / 10);
      loanAmount = parseFloat(calculatedAmount);
    }

    // 验证贷款年限和利率
    if (!this.data.loanYears || this.data.loanYears <= 0) {
      wx.showToast({
        title: '请选择贷款年限',
        icon: 'none'
      });
      return;
    }

    if (!this.data.commercialRate || this.data.commercialRate <= 0) {
      wx.showToast({
        title: '请输入商贷利率',
        icon: 'none'
      });
      return;
    }

    // 跳转到计算结果页面，使用用户输入的商贷利率
    console.log('传递参数:', {
      loanAmount: loanAmount,
      loanYears: this.data.loanYears,
      interestRate: this.data.commercialRate
    });
    
    wx.navigateTo({
      url: `/pages/calculation-result/calculation-result?loanAmount=${loanAmount}&loanYears=${this.data.loanYears}&interestRate=${this.data.commercialRate}`,
      success: () => {
        console.log('成功跳转到房贷计算结果页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载最新利率数据
  loadMortgageRates: function() {
    api.get(api.API.MORTGAGE_RATES_LATEST)
      .then(res => {
        console.log('获取利率数据成功:', res);
        if (res.code === 200 && res.data) {
          const data = res.data;
          // 格式化更新时间为月日格式
          const updateTime = this.formatUpdateTime(data.updateTime);
          
          this.setData({
            mortgageRatesData: data,
            lpr1year: parseFloat((data.lpr1year * 100).toFixed(2)),
            lpr5year: parseFloat((data.lpr5year * 100).toFixed(2)),
            commercialRate: (data.commercialDefaultRate * 100).toFixed(1), // 转换为百分比并保留1位小数
            updateTime: updateTime
          });
          
          // 更新利率选项配置
          this.updateRateOptions();
        }
      })
      .catch(err => {
        console.error('获取利率数据失败:', err);
        // 使用默认值
        this.setData({
          updateTime: '07.06'
        });
      });
  },
  
  // 格式化更新时间为月日格式
  formatUpdateTime: function(timeString) {
    if (!timeString) return '07.06';
    
    try {
      const date = new Date(timeString);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${month}.${day}`;
    } catch (e) {
      console.error('时间格式化失败:', e);
      return '07.06';
    }
  },
  
  // 商贷利率输入
  onCommercialRateInput: function(e) {
    const value = this.validateRateInput(e.detail.value);
    this.setData({
      commercialRate: value
    });
  },
  
  // 验证利率输入（支持小数点）
  validateRateInput: function(value) {
    // 只允许数字和一个小数点
    let cleanValue = value.replace(/[^\d.]/g, '');
    
    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多3位
    if (parts.length === 2 && parts[1].length > 3) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 3);
    }
    
    // 限制范围在0.1-20之间
    const numValue = parseFloat(cleanValue);
    if (numValue > 20) {
      cleanValue = '20';
    } else if (numValue < 0.1 && cleanValue !== '' && cleanValue !== '0' && cleanValue !== '0.') {
      cleanValue = '0.1';
    }
    
    return cleanValue;
  },

  // 显示常见问题
  showFAQ: function() {
    wx.showModal({
      title: '常见问题',
      content: '1. 如何选择合适的贷款年限？\n2. LPR利率是什么？\n3. 等额本息和等额本金的区别？',
      showCancel: false
    });
  }
});
