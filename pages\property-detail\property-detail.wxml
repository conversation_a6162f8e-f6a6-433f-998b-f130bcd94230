<!--pages/property-detail/property-detail.wxml-->
<view class="container" style="position: relative; left: 0rpx; top: 27rpx">



  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view class="main-content" wx:elif="{{!loading && propertyInfo}}">

    <!-- 图片轮播区域 -->
    <view class="image-section">
      <swiper class="property-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" bindchange="onSwiperChange">
        <swiper-item wx:for="{{propertyInfo.images}}" wx:key="index">
          <image class="property-image" src="{{item}}" mode="aspectFill"></image>
        </swiper-item>
      </swiper>

      <!-- 图片计数器 -->
      <view class="image-counter">{{currentImageIndex + 1}}/{{propertyInfo.images.length}}</view>

      <!-- 状态标签 -->
      <view class="status-tag" wx:if="{{propertyInfo.statusText}}">{{propertyInfo.statusText}}</view>
    </view>

    <!-- 楼盘基本信息 -->
    <view class="property-info">
      <view class="property-title">{{propertyInfo.title}}</view>

      <!-- 标签区域 -->
      <view class="tags-section">
        <view class="tag tag-discount" wx:if="{{propertyInfo.discount}}">{{propertyInfo.discount}}</view>
        <view class="tag tag-new" wx:if="{{propertyInfo.tags}}">{{propertyInfo.tags}}</view>
      </view>

      <!-- 价格信息区域 -->
      <view class="price-section">
        <view class="price-row">
          <view class="price-item">
            <view class="price-value">{{propertyInfo.startPrice}}万</view>
            <view class="price-label">起拍价</view>
          </view>
          <view class="price-item">
            <view class="price-value">{{propertyInfo.evaluationPrice}}万</view>
            <view class="price-label">市场价</view>
          </view>
          <view class="price-item">
            <view class="price-value">{{propertyInfo.savingSpace}}万</view>
            <view class="price-label">拍漏空间</view>
          </view>
        </view>

        <view class="price-row">
          <view class="price-item">
            <view class="price-value-black">{{propertyInfo.startingUnitPrice}}万</view>
            <view class="price-label">起拍单价</view>
          </view>
          <view class="price-item">
            <view class="price-value-black">{{propertyInfo.marketUnitPrice}}万</view>
            <view class="price-label">市场单价</view>
          </view>
          <view class="price-item">
            <view class="price-value-black">{{propertyInfo.deposit}}万</view>
            <view class="price-label">保证金</view>
          </view>
        </view>
      </view>

      <!-- 基本信息区域 -->
      <view class="info-section">
        <view class="info-row">
          <view class="info-item">
            <view class="info-label">小区名称</view>
            <view class="info-value">{{propertyInfo.communityName || '暂无'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">建筑面积</view>
            <view class="info-value">{{propertyInfo.buildingArea}}㎡</view>
          </view>
        </view>
        <view class="info-row">
          <view class="info-item">
            <view class="info-label">房屋户型</view>
            <view class="info-value">{{propertyInfo.houseType || '暂无'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">所在楼层</view>
            <view class="info-value">{{propertyInfo.floor || '暂无'}}</view>
          </view>
        </view>
        <view class="info-row">
          <view class="info-item">
            <view class="info-label">建成年份</view>
            <view class="info-value">{{propertyInfo.constructionYear || '暂无'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">装修状况</view>
            <view class="info-value">{{propertyInfo.decoration || '暂无'}}</view>
          </view>
        </view>
        <view class="info-row">
          <view class="info-item">
            <view class="info-label">物业类型</view>
            <view class="info-value">{{propertyInfo.propertyType || '暂无'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">楼梯类型</view>
            <view class="info-value">{{propertyInfo.stairsType || '暂无'}}</view>
          </view>
        </view>
      </view>

      <!-- 拍卖时间 -->
      <view class="auction-time">
        <view class="time-row">
          <view class="time-item">
            <view class="time-label">开始时间</view>
            <view class="time-value">{{propertyInfo.startTime}}</view>
          </view>
        </view>
      </view>

      <!-- 提示信息 -->
      <view class="notice-banner" wx:if="{{propertyInfo.noticeText}}">
        <text class="notice-text">{{propertyInfo.noticeText}}</text>
      </view>
    </view>

    <!-- 相关附件区域 -->
    <view class="attachments-section" wx:if="{{propertyInfo.evaluationReport || propertyInfo.executionOrder || propertyInfo.propertyReport}}">
      <view class="section-title">{{propertyInfo.attachmentsTitle || '相关附件'}}</view>
      <view class="attachments-list">
        <view class="attachment-item" wx:if="{{propertyInfo.evaluationReport}}" bindtap="copyAttachmentLink" data-link="{{propertyInfo.evaluationReport}}" data-name="市场报告">
          <view class="attachment-info">
            <view class="attachment-name">市场报告</view>
            <view class="attachment-desc">点击复制链接到浏览器下载</view>
          </view>
          <view class="attachment-action">复制链接</view>
        </view>
        <view class="attachment-item" wx:if="{{propertyInfo.executionOrder}}" bindtap="copyAttachmentLink" data-link="{{propertyInfo.executionOrder}}" data-name="执行裁定书">
          <view class="attachment-info">
            <view class="attachment-name">执行裁定书</view>
            <view class="attachment-desc">点击复制链接到浏览器下载</view>
          </view>
          <view class="attachment-action">复制链接</view>
        </view>
        <view class="attachment-item" wx:if="{{propertyInfo.propertyReport}}" bindtap="copyAttachmentLink" data-link="{{propertyInfo.propertyReport}}" data-name="房产报告">
          <view class="attachment-info">
            <view class="attachment-name">房产报告</view>
            <view class="attachment-desc">点击复制链接到浏览器下载</view>
          </view>
          <view class="attachment-action">复制链接</view>
        </view>
      </view>
    </view>

    <!-- 图片详情区域 -->
    <view class="image-detail-section" wx:if="{{propertyInfo.detailImages && propertyInfo.detailImages.length > 0}}">
      <view class="section-title">{{propertyInfo.imageDetailTitle || '图片详情(仅供选房参考)'}}</view>

      <!-- 实景图片 -->
      <view class="detail-images-container">
        <view class="detail-images-grid">
          <view class="detail-image-item" wx:for="{{propertyInfo.detailImages}}" wx:key="index" bindtap="previewDetailImage" data-index="{{index}}">
            <image class="detail-image" src="{{item}}" mode="aspectFill"></image>
            <view class="detail-image-overlay">
              <text class="detail-image-overlay-text">点击查看大图</text>
            </view>
          </view>
        </view>
      </view>
    </view>

  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{!loading && error}}">
    <view class="error-icon">⚠️</view>
    <view class="error-title">加载失败</view>
    <view class="error-desc">{{errorMessage}}</view>
    <button class="error-btn" bindtap="loadPropertyDetail">重新加载</button>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar" wx:if="{{!loading && propertyInfo}}">
    <view class="bottom-left">
      <view class="contact-item {{isFollowed ? 'followed' : ''}}" bindtap="toggleFollow">
        <image class="contact-icon" src="/images/favorites.png" mode="aspectFit"></image>
        <text class="contact-text">{{isFollowed ? '已关注' : '关注'}}</text>
      </view>
      <view class="contact-item {{isFavorite ? 'favorited' : ''}}" bindtap="toggleFavorite">
        <image class="contact-icon" src="/images/follow.png" mode="aspectFit"></image>
        <text class="contact-text">{{isFavorite ? '已收藏' : '收藏'}}</text>
      </view>
    </view>
    <view class="bottom-right">
      <button class="consult-btn" bindtap="showConsultModal" style="width: 268rpx; display: block; box-sizing: border-box; left: 52rpx; top: -11rpx; position: relative">电话咨询</button>
    </view>
  </view>

</view>