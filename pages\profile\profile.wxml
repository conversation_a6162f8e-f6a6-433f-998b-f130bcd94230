<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section" style="position: relative; top: -40rpx">
    <view class="user-info" bindtap="onLogin">
      <image class="user-avatar" src="{{userInfo.avatarUrl || '../../images/default-avatar.png'}}" style="position: relative; left: 0rpx; top: 60rpx"></image>
      <view class="user-details">
        <view class="user-name" style="position: relative; left: 131rpx; top: -40rpx">{{userInfo.nickname || '点击登录'}}</view>
      </view>
    </view>
    <view class="qr-code" bindtap="onSettings" style="position: relative; top: 20rpx">
      <image class="qr-icon" src="../../images/settings.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-container">
    <!-- 我的收藏 -->
    <view class="menu-item" bindtap="navigateTo" data-page="favorites" >
      <view class="menu-icon favorites-icon">
        <image class="menu-icon-img" src="../../images/favorites.png" mode="aspectFit" style="width: 66rpx; height: 70rpx; display: block; box-sizing: border-box"></image>
      </view>
      <view class="menu-text">我的收藏</view>
    </view>

    <!-- 我的关注 -->
    <view class="menu-item" bindtap="navigateTo" data-page="follow" >
      <view class="menu-icon follow-icon">
        <image class="menu-icon-img" src="../../images/follow.png" mode="aspectFit" style="width: 75rpx; height: 68rpx; display: block; box-sizing: border-box"></image>
      </view>
      <view class="menu-text">我的关注</view>
    </view>

    <!-- 我的浏览 -->
    <view class="menu-item" bindtap="navigateTo" data-page="browse" >
      <view class="menu-icon browse-icon">
        <image class="menu-icon-img" src="../../images/browse.png" mode="aspectFit" style="width: 106rpx; height: 122rpx; display: block; box-sizing: border-box"></image>
      </view>
      <view class="menu-text">直播看房</view>

    </view>

    <!-- 关于我们 -->
    <view class="menu-item" bindtap="navigateTo" data-page="about" >
      <view class="menu-icon about-icon">
        <image class="menu-icon-img" src="../../images/about.png" mode="aspectFit" style="width: 181rpx; height: 164rpx; display: block; box-sizing: border-box"></image>
      </view>
      <view class="menu-content">
        <view class="menu-text">关于我们</view>
        <view class="menu-subtitle">获取最新政策、咨询</view>
      </view>

    </view>
  </view>

  <!-- 新用户授权弹窗 -->
  <view class="auth-modal" wx:if="{{showAuthModal}}" bindtap="hideAuthModal">
    <view class="auth-modal-content" catchtap="preventClose">
      <view class="auth-modal-header">
        <text class="auth-modal-title">完善个人信息</text>
        <view class="auth-modal-close" bindtap="hideAuthModal">×</view>
      </view>

      <view class="auth-modal-body">
        <view class="auth-tip">
          <text class="auth-tip-text">为了给您提供更好的服务，请完善以下信息：</text>
        </view>

        <!-- 头像授权 -->
        <view class="auth-item {{authStatus.avatar ? 'completed' : ''}}" wx:if="{{!authStatus.avatar}}">
          <view class="auth-item-left">
            <image class="auth-icon" src="../../images/avatar-icon.png"></image>
            <text class="auth-text">头像</text>
          </view>
          <button class="auth-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <text class="auth-btn-text">授权</text>
          </button>
        </view>

        <!-- 昵称授权 -->
        <view class="auth-item {{authStatus.nickname ? 'completed' : ''}}" wx:if="{{!authStatus.nickname}}">
          <view class="auth-item-left">
            <image class="auth-icon" src="../../images/nickname-icon.png"></image>
            <text class="auth-text">昵称</text>
          </view>
          <input class="auth-input"
                 type="nickname"
                 placeholder="请输入昵称"
                 bindinput="onNicknameInput"
                 value="{{tempNickname}}" />
        </view>

        <!-- 手机号授权 -->
        <view class="auth-item {{authStatus.phone ? 'completed' : ''}}" wx:if="{{!authStatus.phone}}">
          <view class="auth-item-left">
            <image class="auth-icon" src="../../images/phone-icon.png"></image>
            <text class="auth-text">手机号</text>
          </view>
          <button class="auth-btn" open-type="getPhoneNumber" bindgetphonenumber="onGetPhoneNumber">
            <text class="auth-btn-text">授权</text>
          </button>
        </view>

        <!-- 已完成的信息显示 -->
        <view class="completed-info" wx:if="{{authStatus.avatar || authStatus.nickname || authStatus.phone}}">
          <view class="completed-title">已完成：</view>
          <view class="completed-item" wx:if="{{authStatus.avatar}}">
            <image class="completed-icon" src="../../images/check.png"></image>
            <text class="completed-text">头像</text>
          </view>
          <view class="completed-item" wx:if="{{authStatus.nickname}}">
            <image class="completed-icon" src="../../images/check.png"></image>
            <text class="completed-text">昵称</text>
          </view>
          <view class="completed-item" wx:if="{{authStatus.phone}}">
            <image class="completed-icon" src="../../images/check.png"></image>
            <text class="completed-text">手机号</text>
          </view>
        </view>
      </view>

      <view class="auth-modal-footer">
        <view class="auth-skip-btn" bindtap="skipAuth">跳过</view>
        <view class="auth-complete-btn {{canComplete ? 'active' : ''}}" bindtap="completeAuth">
          完成 ({{completedCount}}/3)
        </view>
      </view>
    </view>
  </view>
</view>