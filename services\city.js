/**
 * 城市服务
 * 处理城市选择和区域映射逻辑
 */

// 重庆市区的区域列表
const CHONGQING_DISTRICTS = [
  '沙坪坝区',
  '渝中区', 
  '江北区',
  '南岸区',
  '九龙坡区',
  '大渡口区',
  '渝北区',
  '巴南区',
  '北碚区'
]

// 重庆郊区的区域列表
const CHONGQING_SUBURBAN = [
  '江津区',
  '合川区',
  '永川区',
  '璧山区',
  '荣昌区',
  '大足区',
  '铜梁区',
  '潼南区',
  '涪陵区',
  '长寿区',
  '南川区',
  '綦江区',
  '万盛经开区',
  '垫江县'
]

/**
 * 根据城市选择获取对应的区域列表
 * @param {string} selectedCity 选中的城市
 * @returns {Array} 区域列表
 */
const getDistrictsByCity = (selectedCity) => {
  switch (selectedCity) {
    case '重庆市区':
      return CHONGQING_DISTRICTS
    case '重庆郊区':
      return CHONGQING_SUBURBAN
    default:
      return []
  }
}

/**
 * 判断是否为重庆城市（市区或郊区）
 * @param {string} selectedCity 选中的城市
 * @returns {boolean} 是否为重庆城市
 */
const isChongqingCity = (selectedCity) => {
  return selectedCity === '重庆市区' || selectedCity === '重庆郊区'
}

/**
 * 获取当前选中的城市
 * @returns {string} 当前选中的城市
 */
const getCurrentCity = () => {
  return wx.getStorageSync('selectedCity') || '重庆市区'
}

/**
 * 格式化城市名称显示（添加"市"后缀，重庆除外）
 * @param {string} cityName 原始城市名称
 * @returns {string} 格式化后的城市名称
 */
const formatCityDisplay = (cityName) => {
  if (!cityName) return cityName
  
  // 重庆市区和重庆郊区保持原样
  if (cityName === '重庆市区' || cityName === '重庆郊区') {
    return cityName
  }
  
  // 如果已经包含"市"字，不重复添加
  if (cityName.includes('市')) {
    return cityName
  }
  
  // 其他城市添加"市"后缀
  return cityName + '市'
}

/**
 * 格式化城市名称用于API调用（移除"市"后缀）
 * @param {string} cityName 显示用的城市名称
 * @returns {string} API调用用的城市名称
 */
const formatCityForAPI = (cityName) => {
  if (!cityName) return cityName
  
  // 重庆市区和重庆郊区保持原样
  if (cityName === '重庆市区' || cityName === '重庆郊区') {
    return cityName
  }
  
  // 移除"市"后缀用于API调用
  return cityName.replace(/市$/, '')
}

/**
 * 获取房源数据的API调用配置
 * @param {string} selectedCity 选中的城市
 * @returns {Object} API调用配置
 */
const getHouseApiConfig = (selectedCity) => {
  const config = {
    isChongqing: isChongqingCity(selectedCity),
    districts: [],
    city: formatCityForAPI(selectedCity) // API调用使用不带"市"的城市名
  }

  if (config.isChongqing) {
    config.districts = getDistrictsByCity(selectedCity)
  }

  return config
}

module.exports = {
  CHONGQING_DISTRICTS,
  CHONGQING_SUBURBAN,
  getDistrictsByCity,
  isChongqingCity,
  getCurrentCity,
  formatCityDisplay,
  formatCityForAPI,
  getHouseApiConfig
}