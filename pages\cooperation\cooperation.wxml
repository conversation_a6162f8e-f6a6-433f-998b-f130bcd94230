<!--pages/cooperation/cooperation.wxml-->
<view class="container">
  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 咨询信息表单 -->
    <view class="form-section">

      <!-- 咨询城市 -->
      <view class="form-item">
        <view class="form-label">咨询城市</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入咨询城市" value="{{consultCity}}" bindinput="onConsultCityInput"/>
        </view>
      </view>

      <!-- 联系人姓名 -->
      <view class="form-item">
        <view class="form-label">联系人姓名</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入联系人姓名" value="{{contactName}}" bindinput="onContactNameInput"/>
        </view>
      </view>

      <!-- 先生/女士 -->
      <picker range="{{genderOptions}}" value="{{genderIndex}}" bindchange="onGenderChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-item">
          <view class="form-label">先生/女士</view>
          <view class="form-value-container">
            <text class="form-value">{{genderOptions[genderIndex]}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 联系方式 -->
      <view class="form-item">
        <view class="form-label">联系方式</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入联系方式" value="{{contactPhone}}" bindinput="onContactPhoneInput" type="number"/>
        </view>
      </view>

      <!-- 咨询内容 -->
      <view class="form-item" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-label">咨询内容</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入咨询内容" value="{{consultContent}}" bindinput="onConsultContentInput"/>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="form-section">
      <view class="section-title">备注</view>
      <view class="form-item textarea-item">
        <textarea class="form-textarea" placeholder="请输入" value="{{remarks}}" bindinput="onRemarksInput" maxlength="200"></textarea>
      </view>
    </view>

    <!-- 咨询记录链接 -->
    <view class="submit-record">
      <text class="record-link" bindtap="viewConsultRecord">咨询记录>></text>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-container">
    <button class="submit-btn" bindtap="onSubmit" style="width: 602rpx; display: block; box-sizing: border-box; left: 0rpx; top: 0rpx">确认提交</button>
  </view>
</view>
