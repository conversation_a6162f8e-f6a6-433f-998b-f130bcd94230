// pages/house-sale-records/house-sale-records.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const util = require('../../utils/util.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    recordList: [],
    loading: true,
    error: false,
    errorMessage: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('房源出售记录页面加载')
    this.checkLoginAndLoadRecords()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载数据
    this.checkLoginAndLoadRecords()
  },

  /**
   * 检查登录状态并加载记录
   */
  checkLoginAndLoadRecords() {
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      this.setData({
        loading: false,
        error: true,
        errorMessage: '请先登录后查看房源记录'
      })
      
      // 延迟显示登录提示
      setTimeout(() => {
        wx.showModal({
          title: '需要登录',
          content: '查看房源记录需要先登录，是否现在去登录？',
          showCancel: true,
          cancelText: '取消',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({
                url: '/pages/profile/profile'
              })
            } else {
              wx.navigateBack()
            }
          }
        })
      }, 500)
      return
    }

    // 已登录，加载房源记录
    this.loadRecords()
  },

  /**
   * 加载房源记录
   */
  async loadRecords() {
    try {
      this.setData({
        loading: true,
        error: false,
        errorMessage: ''
      })

      // 获取当前用户信息
      const currentUser = userService.getLocalUserInfo()
      if (!currentUser || !currentUser.id) {
        throw new Error('获取用户信息失败')
      }

      // 获取用户token
      const accessToken = wx.getStorageSync('accessToken')
      if (!accessToken) {
        throw new Error('用户未登录')
      }

      console.log('开始加载房源记录，用户ID:', currentUser.id)

      // 调用查询房源记录API
      const response = await api.get(api.API.HOUSE_SALE_USER, 
        { userId: currentUser.id }, 
        { 'Authorization': `Bearer ${accessToken}` }
      )

      console.log('房源记录API响应:', response)

      if (response.code === 200) {
        // 处理数据格式
        const recordList = (response.data || []).map(item => ({
          ...item,
          createTime: this.formatTime(item.createTime),
          expectedPrice: this.formatPrice(item.expectedPrice)
        }))

        this.setData({
          recordList: recordList,
          loading: false,
          error: false
        })

        console.log('房源记录加载成功，共', recordList.length, '条记录')
      } else {
        throw new Error(response.message || '获取房源记录失败')
      }

    } catch (error) {
      console.error('加载房源记录失败:', error)
      
      this.setData({
        loading: false,
        error: true,
        recordList: []
      })

      const errorMessage = error.message || error.errMsg || '加载失败'
      if (errorMessage.includes('登录') || errorMessage.includes('401')) {
        this.setData({
          errorMessage: '登录已过期，请重新登录'
        })
      } else if (errorMessage.includes('网络')) {
        this.setData({
          errorMessage: '网络连接失败，请检查网络'
        })
      } else {
        this.setData({
          errorMessage: '加载失败，请重试'
        })
      }
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '暂无时间'
    
    try {
      const date = new Date(timeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      console.error('时间格式化失败:', error)
      return timeStr
    }
  },

  /**
   * 格式化价格
   */
  formatPrice(price) {
    if (!price) return '0'
    
    try {
      // 将价格转换为万元单位
      const priceInWan = (parseFloat(price) / 10000).toFixed(1)
      return priceInWan
    } catch (error) {
      console.error('价格格式化失败:', error)
      return price
    }
  },

  /**
   * 跳转到房源出售页面
   */
  goToSellHouse() {
    wx.navigateTo({
      url: '/pages/sell-house/sell-house'
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新房源记录')
    this.loadRecords().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onReachBottom() {
    // 暂时不实现分页加载
    console.log('到达页面底部')
  }

})
