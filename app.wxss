/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
}

/* 通用按钮样式 */
.btn {
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: #D32F2F;
  color: white;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

/* 通用卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  padding: 30rpx;
}

/* 文本样式 */
.text-primary {
  color: #D32F2F;
}

.text-secondary {
  color: #666;
}

.text-large {
  font-size: 36rpx;
  font-weight: bold;
}

.text-medium {
  font-size: 28rpx;
}

.text-small {
  font-size: 24rpx;
}
