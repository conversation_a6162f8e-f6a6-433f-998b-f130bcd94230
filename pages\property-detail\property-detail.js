// pages/property-detail/property-detail.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const houseService = require('../../services/house.js')
// const util = require('../../utils/util.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,
    propertyInfo: null,
    loading: false,
    error: false,
    errorMessage: '',
    currentImageIndex: 0,
    isFavorite: false,
    isFollowed: false,
    propertyId: null,
    userId: null // 用户ID
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('楼盘详情页面开始加载，参数:', options)

    try {
      // 获取系统信息
      this.getSystemInfo()

      // 直接使用默认ID，跳过参数检查
      const propertyId = options.id || options.propertyId || '123'
      console.log('使用的楼盘ID:', propertyId)

      // 获取用户ID（从本地存储或全局数据）
      const userId = wx.getStorageSync('userId') || 1; // 默认用户ID为1

      this.setData({
        propertyId: propertyId,
        userId: userId
      })
      console.log('开始加载楼盘详情数据')
      this.loadPropertyDetail(propertyId)

    } catch (error) {
      console.error('页面加载过程中发生错误:', error)
      this.setData({
        loading: false,
        error: true,
        errorMessage: '页面初始化失败: ' + error.message
      })
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 使用统一的登录状态检查方法（与收藏和关注保持一致）
    const isLoggedIn = userService.checkLoginStatus()
    const currentUserId = isLoggedIn ? wx.getStorageSync('userId') : null;

    if (currentUserId !== this.data.userId) {
      // 用户状态发生变化，更新userId并重新检查状态
      this.setData({
        userId: currentUserId || null
      });
      this.checkUserStatus();
    }
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight || 44
    })
  },

  /**
   * 加载楼盘详情数据
   * API接入位置：调用楼盘详情接口
   */
  loadPropertyDetail(propertyId) {
    console.log('开始加载楼盘详情，ID:', propertyId)

    this.setData({
      loading: true,
      error: false
    });

    // 调用真实API获取房源详情
    api.get(`${api.API.HOUSE_DETAIL}/${propertyId}`)
      .then(res => {
        console.log('房源详情API响应:', res);

        // 根据API文档检查响应格式
        if (res.code === 200 && res.data) {
          const houseData = res.data;
          console.log('房源详情数据:', houseData);

          const formattedData = this.formatPropertyData(houseData);
          this.setData({
            propertyInfo: formattedData,
            loading: false,
            error: false,
            isFavorite: false,
            isFollowed: false
          });

          // 检查用户的关注和收藏状态
          this.checkUserStatus();

          console.log('房源详情设置成功');
        } else {
          console.log('API响应失败，code:', res.code, 'message:', res.message);
          throw new Error(res.message || '房源数据获取失败');
        }
      })
      .catch(err => {
        console.error('加载房源详情失败:', err);
        // API失败时使用模拟数据
        this.loadMockData(propertyId);
      });
  },

  // 格式化房源数据
  formatPropertyData: function(houseData) {
    console.log('格式化房源数据:', houseData);

    const startPrice = (houseData.startingPrice / 10000).toFixed(2);
    const evaluationPrice = (houseData.evaluationPrice / 10000).toFixed(2);
    const savingSpace = (evaluationPrice - startPrice).toFixed(2);
    const discountRate = houseData.discountRate || 80;

    // 格式化折扣率
    const formatDiscountRate = (rate) => {
      if (!rate || rate >= 100) return null
      return (rate / 10).toFixed(1) + '折'
    }

    // 格式化单价显示（以万为单位，显示0.xx万的形式）
    const formatUnitPrice = (unitPrice) => {
      if (!unitPrice || unitPrice <= 0) return '0.00'
      // 将单价转换为万元单位，保留3位小数
      const unitPriceInWan = (unitPrice / 10000).toFixed(2)
      return unitPriceInWan
    }

    return {
      id: houseData.id,
      title: houseData.title || '房源标题',

      // 状态相关
      status: this.getPropertyStatus(houseData),
      statusText: this.getPropertyStatusText(houseData),

      // 标签相关
      discount: formatDiscountRate(discountRate),
      isNew: houseData.isSpecial === 1,
      newHouseText: houseData.isSpecial === 1 ? '特殊资产' : '普通房源',
      isSelected: houseData.isSelected === 1,
      selectedText: houseData.isSelected === 1 ? '精选房源' : '',
      tags: houseData.tags || '',

      // 基本信息
      communityName: houseData.communityName || '',
      buildingArea: houseData.buildingArea || 0,
      houseType: houseData.houseType || '',
      floor: houseData.floor || '',
      constructionYear: houseData.constructionYear || '',
      decoration: this.getDecorationText(houseData.decoration),
      propertyType: this.getPropertyTypeText(houseData.propertyType),
      stairsType: this.getStairsTypeText(houseData.stairsType),

      // 价格相关数据
      startPrice: startPrice,
      startPriceLabel: '起拍价',
      evaluationPrice: evaluationPrice,
      evaluationPriceLabel: '市场价',
      savingSpace: savingSpace,
      savingSpaceLabel: '拍漏空间',
      startingUnitPrice: formatUnitPrice(houseData.startingUnitPrice || 0),
      startingUnitPriceLabel: '起拍单价',
      marketUnitPrice: formatUnitPrice(houseData.marketUnitPrice || 0),
      marketUnitPriceLabel: '市场单价',

      // 拍卖信息
      auctionCycle: houseData.auctionCycle || 0,
      auctionTimes: houseData.auctionTimes || 1,
      deposit: (houseData.deposit / 10000).toFixed(0) || '0',
      priceIncrement: (houseData.priceIncrement / 10000).toFixed(0) || '0',
      bargainSpace: (houseData.bargainSpace / 10000).toFixed(0) || '0',

      // 时间相关
      startTime: houseData.startTime || '',
      endTime: houseData.endTime || '',
      auctionTimeLabel: '拍卖时间',

      // 提示信息
      noticeText: '标的物详情以法院拍卖公告为准',

      // 拍卖信息相关
      auctionSectionTitle: '拍卖信息',
      communityAuctionCount: 1,
      communityAuctionLabel: '该小区待拍/拍卖中房源',
      followBtnText: '关注小区',

      // 图片详情相关
      imageDetailTitle: '图片详情(仅供选房参考)',
      detailImages: this.parseImageList(houseData.imageUrls),

      // 原始链接
      originalUrl: houseData.originalUrl || '',

      // 轮播图片相关
      images: this.parseImageList(houseData.imageUrls)
    };
  },

  // 获取房源状态
  getPropertyStatus: function(houseData) {
    const now = new Date();
    const startTime = new Date(houseData.startTime);
    const endTime = new Date(houseData.endTime);

    if (now < startTime) {
      return 'coming';
    } else if (now >= startTime && now <= endTime) {
      return 'ongoing';
    } else {
      return 'ended';
    }
  },

  // 获取房源状态文本
  getPropertyStatusText: function(houseData) {
    const now = new Date();
    const startTime = new Date(houseData.startTime);
    const endTime = new Date(houseData.endTime);

    if (now < startTime) {
      return '即将开始';
    } else if (now >= startTime && now <= endTime) {
      return '拍卖中';
    } else {
      return '已结束';
    }
  },

  // 解析图片列表
  parseImageList: function(imageListStr) {
    if (!imageListStr) {
      return ['/images/aaa.png']; // 默认图片
    }

    const images = imageListStr.split(',')
      .map(img => img.trim())
      .filter(img => img && img.length > 0);

    console.log('解析图片列表:', images);
    return images.length > 0 ? images : ['/images/aaa.png'];
  },

  // 获取第一张图片
  getFirstImage: function(imageListStr) {
    const images = this.parseImageList(imageListStr);
    return images[0] || '/images/aaa.png';
  },

  // 获取装修状态文本
  getDecorationText: function(decoration) {
    const decorationMap = {
      0: '毛坯',
      1: '简装',
      2: '精装',
      3: '豪装'
    };
    return decorationMap[decoration] || '未知';
  },

  // 获取房产类型文本
  getPropertyTypeText: function(propertyType) {
    const propertyTypeMap = {
      0: '低层',
      1: '中层',
      2: '高层',
      4: '其他'
    };
    return propertyTypeMap[propertyType] || '未知';
  },

  // 获取楼梯类型文本
  getStairsTypeText: function(stairsType) {
    const stairsTypeMap = {
      0: '楼梯房',
      1: '电梯房',
      2: '复式',
      3: '别墅'
    };
    return stairsTypeMap[stairsType] || '未知';
  },

  // 复制附件链接
  copyAttachmentLink: function(e) {
    const { link, name } = e.currentTarget.dataset;
    if (!link) {
      wx.showToast({
        title: '链接不存在',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: `链接已复制`,
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 预览情况调查表
  previewFloorPlan: function() {
    if (!this.data.propertyInfo.floorPlan) {
      wx.showToast({
        title: '暂无情况调查表',
        icon: 'none'
      });
      return;
    }

    wx.previewImage({
      current: this.data.propertyInfo.floorPlan,
      urls: [this.data.propertyInfo.floorPlan]
    });
  },

  // 预览实景图片
  previewDetailImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.propertyInfo.detailImages;

    if (!images || images.length === 0) {
      wx.showToast({
        title: '暂无图片',
        icon: 'none'
      });
      return;
    }

    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  // 预览地图图片
  previewMapImage: function() {
    const mapImage = this.data.propertyInfo.mapImage;

    if (!mapImage || mapImage === '/images/map.png') {
      wx.showToast({
        title: '暂无地图信息',
        icon: 'none'
      });
      return;
    }

    wx.previewImage({
      current: mapImage,
      urls: [mapImage]
    });
  },

  // 切换关注状态
  toggleFollow: function() {
    const { propertyId, isFollowed } = this.data;

    // 使用统一的登录状态检查方法（与其他功能保持一致）
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '关注房源需要先登录，是否现在去登录？',
        showCancel: true,
        cancelText: '取消',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          }
        }
      });
      return;
    }

    const userId = wx.getStorageSync('userId');

    if (!userId) {
      wx.showToast({
        title: '用户信息错误，请重新登录',
        icon: 'none'
      });
      return;
    }

    if (!propertyId) {
      wx.showToast({
        title: '房源信息错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: isFollowed ? '取消关注中...' : '关注中...'
    });

    // 根据当前状态调用不同的API
    const apiUrl = `${api.API.USER_FOLLOW}/${userId}/follow?houseId=${propertyId}`;
    console.log('关注API URL:', api.baseUrl + apiUrl);
    console.log('当前关注状态:', isFollowed, '操作:', isFollowed ? '取消关注' : '添加关注');

    // 如果当前已关注，调用DELETE接口取消关注；否则调用POST接口添加关注
    const apiMethod = isFollowed ? api.del : api.post;

    apiMethod(apiUrl)
      .then(res => {
        console.log('关注API响应:', res);

        if (res.code === 200) {
          const newFollowState = !isFollowed;
          this.setData({
            isFollowed: newFollowState
          });

          // 保存状态到本地存储
          const followKey = `follow_${userId}_${propertyId}`;
          wx.setStorageSync(followKey, newFollowState);

          wx.showToast({
            title: newFollowState ? '关注成功' : '取消关注成功',
            icon: 'success'
          });
        } else {
          throw new Error(res.message || '操作失败');
        }
      })
      .catch(err => {
        console.error('关注操作失败:', err);
        wx.showToast({
          title: err.message || '操作失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 切换收藏状态
  toggleFavorite: function() {
    const { propertyId, isFavorite } = this.data;

    // 使用统一的登录状态检查方法（与其他功能保持一致）
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '收藏房源需要先登录，是否现在去登录？',
        showCancel: true,
        cancelText: '取消',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          }
        }
      });
      return;
    }

    const userId = wx.getStorageSync('userId');

    if (!userId) {
      wx.showToast({
        title: '用户信息错误，请重新登录',
        icon: 'none'
      });
      return;
    }

    if (!propertyId) {
      wx.showToast({
        title: '房源信息错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: isFavorite ? '取消收藏中...' : '收藏中...'
    });

    // 根据当前状态调用不同的API
    const apiUrl = `${api.API.USER_FAVORITE}/${userId}/favorite?houseId=${propertyId}`;
    console.log('收藏API URL:', api.baseUrl + apiUrl);
    console.log('当前收藏状态:', isFavorite, '操作:', isFavorite ? '取消收藏' : '添加收藏');

    // 如果当前已收藏，调用DELETE接口取消收藏；否则调用POST接口添加收藏
    const apiMethod = isFavorite ? api.del : api.post;

    apiMethod(apiUrl)
      .then(res => {
        console.log('收藏API响应:', res);

        if (res.code === 200) {
          const newFavoriteState = !isFavorite;
          this.setData({
            isFavorite: newFavoriteState
          });

          // 保存状态到本地存储
          const favoriteKey = `favorite_${userId}_${propertyId}`;
          wx.setStorageSync(favoriteKey, newFavoriteState);

          wx.showToast({
            title: newFavoriteState ? '收藏成功' : '取消收藏成功',
            icon: 'success'
          });
        } else {
          throw new Error(res.message || '操作失败');
        }
      })
      .catch(err => {
        console.error('收藏操作失败:', err);
        wx.showToast({
          title: err.message || '操作失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 检查用户的关注和收藏状态
  checkUserStatus: function() {
    const { propertyId } = this.data;

    // 使用统一的登录状态检查方法（与其他功能保持一致）
    const isLoggedIn = userService.checkLoginStatus()

    if (!isLoggedIn || !propertyId) {
      console.log('用户未登录或房源ID不存在，跳过状态检查');
      // 用户未登录时，重置状态
      this.setData({
        userId: null,
        isFollowed: false,
        isFavorite: false
      });
      return;
    }

    // 获取当前用户ID
    const currentUserId = wx.getStorageSync('userId');
    this.setData({
      userId: currentUserId
    });

    // 这里可以调用API检查用户的关注和收藏状态
    // 由于API文档中没有提供查询状态的接口，我们暂时使用本地存储来模拟
    const followKey = `follow_${currentUserId}_${propertyId}`;
    const favoriteKey = `favorite_${currentUserId}_${propertyId}`;

    const isFollowed = wx.getStorageSync(followKey) || false;
    const isFavorite = wx.getStorageSync(favoriteKey) || false;

    this.setData({
      isFollowed: isFollowed,
      isFavorite: isFavorite
    });

    console.log('用户状态检查完成:', { isFollowed, isFavorite });
  },

  /**
   * 检查收藏状态
   * API接入位置：调用检查收藏状态接口
   */
  async checkFavoriteStatus(propertyId) {
    try {
      // 暂时跳过登录检查
      // const isLoggedIn = userService.checkLoginStatus()
      // if (!isLoggedIn) {
      //   return
      // }

      // TODO: 调用检查收藏状态API
      // const response = await api.get(api.API.CHECK_FAVORITE, { propertyId })

      // 模拟收藏状态
      const isFavorite = false
      this.setData({
        isFavorite: isFavorite
      })

    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  },

  /**
   * 轮播图切换事件
   */
  onSwiperChange(e) {
    this.setData({
      currentImageIndex: e.detail.current
    })
  },

  /**
   * 预览户型图
   */
  previewFloorPlan() {
    const { propertyInfo } = this.data
    if (propertyInfo && propertyInfo.floorPlan) {
      wx.previewImage({
        current: propertyInfo.floorPlan,
        urls: [propertyInfo.floorPlan]
      })
    }
  },

  /**
   * 预览实景图片
   */
  previewDetailImage(e) {
    const { index } = e.currentTarget.dataset
    const { propertyInfo } = this.data
    if (propertyInfo && propertyInfo.detailImages && propertyInfo.detailImages.length > 0) {
      wx.previewImage({
        current: propertyInfo.detailImages[index],
        urls: propertyInfo.detailImages
      })
    }
  },



  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    })
  },



  /**
   * 显示在线咨询
   */
  showContact() {
    wx.showModal({
      title: '在线咨询',
      content: '是否联系客服进行咨询？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '联系客服',
      success: (res) => {
        if (res.confirm) {
          // TODO: 实现在线咨询功能
          wx.showToast({
            title: '正在为您接通客服...',
            icon: 'loading',
            duration: 2000
          })
        }
      }
    })
  },

  /**
   * 显示电话咨询弹窗
   */
  showConsultModal() {
    wx.showModal({
      title: '电话咨询',
      content: '客服电话：15826139279',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '15826139279',
            success: () => {
              console.log('拨打电话成功')
            },
            fail: (error) => {
              console.error('拨打电话失败:', error)
              wx.showToast({
                title: '拨打失败，请手动拨打',
                icon: 'none',
                duration: 2000
              })
            }
          })
        }
      }
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新楼盘详情')
    const propertyId = this.data.propertyId
    if (propertyId) {
      this.loadPropertyDetail(propertyId).finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('到达页面底部')
  },

  /**
   * 切换周边功能标签
   */
  switchNearbyTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      nearbyActiveTab: tab,
      facilityActiveTag: tab === 'facilities' ? 'transport' : ''
    })
  },

  /**
   * 切换配套设施标签
   */
  switchFacilityTag(e) {
    const { tag } = e.currentTarget.dataset
    this.setData({
      facilityActiveTag: tag
    })
  },

  /**
   * 查看更多房源
   */
  viewMoreProperties() {
    wx.navigateTo({
      url: '/pages/property-list/property-list?type=nearby'
    })
  },

  /**
   * 查看房源详情
   */
  viewPropertyDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/property-detail/property-detail?id=${id}`
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const propertyInfo = this.data.propertyInfo
    return {
      title: propertyInfo ? propertyInfo.title : '楼盘详情',
      path: `/pages/property-detail/property-detail?id=${this.data.propertyId}`,
      imageUrl: propertyInfo && propertyInfo.images && propertyInfo.images.length > 0 ? propertyInfo.images[0] : ''
    }
  }

})
