// pages/house-list/house-list.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const houseService = require('../../services/house.js')
const cityService = require('../../services/city.js')

Page({
  data: {
    // 房源列表数据
    houseList: [],
    // 页面类型和标题
    pageType: '',
    statsType: '',
    pageTitle: '房源列表',
    searchKeyword: '',
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    hasMore: true,
    loading: false,
    // 筛选面板显示状态
    showRegionPanel: false,
    showAreaPanel: false,
    showPricePanel: false,
    showMorePanel: false,
    showSortPanel: false,
    // 当前筛选条件（支持多选）
    regionFilter: [],
    areaFilter: [],
    priceFilter: [],
    auctionType: [],
    auctionStatus: [],
    sortType: 'smart',
    customMinPrice: '',
    customMaxPrice: '',
    // 临时筛选条件（用于面板中的选择）
    tempRegionFilter: [],
    tempAreaFilter: [],
    tempPriceFilter: [],
    tempAuctionType: [],
    tempAuctionStatus: [],
    tempSortType: 'smart',
    // 筛选文本显示
    regionFilterText: '区域',
    areaFilterText: '面积',
    priceFilterText: '价格',
    sortFilterText: '智能排序',
    // 城市和区域选项
    selectedCity: '重庆市区',
    currentRegionOptions: ['渝中区', '江北区', '南岸区', '沙坪坝区', '九龙坡区', '大渡口区', '渝北区', '巴南区', '北碚区'],
    // 选中状态
    regionActiveStatus: {},
    areaActiveStatus: {},
    priceActiveStatus: {},
    auctionTypeActiveStatus: {},
    auctionStatusActiveStatus: {}
  },

  onLoad: function (options) {
    const type = options.type || options.filterType || 'default';
    const statsType = options.statsType || '';
    const searchKeyword = options.search ? decodeURIComponent(options.search) : '';
    let pageTitle = '房源列表';

    if (statsType) {
      switch (statsType) {
        case 'todayNew':
          pageTitle = '今日新增房源';
          break;
        case 'auctioning':
          pageTitle = '正在拍卖房源';
          break;
        case 'upcoming':
          pageTitle = '即将拍卖房源';
          break;
      }
    } else {
      switch (type) {
        case 'house':
        case 'auction-house':
          pageTitle = '法拍住宅';
          break;
        case 'commercial':
        case 'auction-commercial':
          pageTitle = '法拍商办';
          break;
        case 'special':
        case 'special-assets':
          pageTitle = '特殊资产';
          break;
        case 'premium':
        case 'premium-house':
          pageTitle = '精选房源';
          break;
        case 'user-follows':
          pageTitle = '我的关注';
          break;
        case 'user-favorites':
          pageTitle = '我的收藏';
          break;
        default:
          pageTitle = searchKeyword ? '搜索结果' : '房源列表';
          break;
      }
    }

    // 处理统计筛选参数
    let isStatsFilter = false;
    let createTimeStart = '';
    let createTimeEnd = '';
    let auctionTimeFilter = '';
    let currentTime = '';

    if (statsType) {
      isStatsFilter = true;

      // 处理时间筛选参数
      if (options.createTimeStart) {
        createTimeStart = options.createTimeStart;
        createTimeEnd = options.createTimeEnd || options.createTimeStart;
      }

      if (options.auctionTimeFilter && options.currentTime) {
        auctionTimeFilter = options.auctionTimeFilter;
        currentTime = decodeURIComponent(options.currentTime);
      }
    }

    this.setData({
      pageType: type,
      statsType: statsType,
      pageTitle: pageTitle,
      searchKeyword: searchKeyword,
      isStatsFilter: isStatsFilter,
      createTimeStart: createTimeStart,
      createTimeEnd: createTimeEnd,
      auctionTimeFilter: auctionTimeFilter,
      currentTime: currentTime
    });

    wx.setNavigationBarTitle({
      title: pageTitle
    });

    // 初始化区域选项
    this.updateRegionOptions();

    this.loadHouseList(true);
  },

  onShow: function () {
    this.loadHouseList(true);
  },

  onPullDownRefresh: function () {
    this.loadHouseList(true);
  },

  onReachBottom: function () {
    this.loadMore();
  },

  loadHouseList: function (refresh = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    if (refresh) {
      this.setData({
        currentPage: 1,
        houseList: [],
        hasMore: true
      });
    }

    const queryParams = {
      pageNum: this.data.currentPage,
      pageSize: this.data.pageSize
    };

    // 添加城市过滤
    const selectedCity = cityService.getCurrentCity()
    
    // 所有城市都使用统一的城市过滤
    queryParams.city = selectedCity

    // 注意：统计数据跳转不在API请求中添加筛选参数，而是在前端过滤全部数据

    // 处理多选筛选条件
    if (this.data.regionFilter.length > 0) {
      queryParams.regionList = this.data.regionFilter;
    }
    if (this.data.areaFilter.length > 0) {
      queryParams.areaRangeList = this.data.areaFilter;
    }
    if (this.data.priceFilter.length > 0) {
      queryParams.priceRangeList = this.data.priceFilter;
    }
    if (this.data.auctionType.length > 0) {
      queryParams.auctionTypeList = this.data.auctionType;
    }
    if (this.data.auctionStatus.length > 0) {
      queryParams.auctionStatusList = this.data.auctionStatus;
    }

    let apiPromise;

    // 优先使用统一的分页查询API
    if (this.data.statsType || this.data.regionFilter.length > 0 || this.data.areaFilter.length > 0 ||
        this.data.priceFilter.length > 0 || this.data.auctionType.length > 0 || this.data.auctionStatus.length > 0) {
      apiPromise = api.post(api.API.HOUSE_PAGE, queryParams);
    } else {
      switch (this.data.pageType) {
        case 'house':
        case 'auction-house':
          queryParams.houseCategory = 0;
          apiPromise = api.post(api.API.HOUSE_PAGE, queryParams);
          break;
        case 'commercial':
        case 'auction-commercial':
          queryParams.houseCategory = 1;
          apiPromise = api.post(api.API.HOUSE_PAGE, queryParams);
          break;
        case 'special':
        case 'special-assets':
          queryParams.isSpecial = 1;
          apiPromise = api.post(api.API.HOUSE_PAGE, queryParams);
          break;
        case 'premium':
        case 'premium-house':
          queryParams.isSelected = 1;
          apiPromise = api.post(api.API.HOUSE_PAGE, queryParams);
          break;
        case 'user-follows':
          const userId = wx.getStorageSync('userId') || 1;
          apiPromise = api.get(`${api.API.USER_FOLLOWS}/${userId}/follows`);
          break;
        case 'user-favorites':
          const userIdFav = wx.getStorageSync('userId') || 1;
          apiPromise = api.get(`${api.API.USER_FAVORITES}/${userIdFav}/favorites`);
          break;
        default:
          if (this.data.searchKeyword) {
            apiPromise = houseService.searchHouses(this.data.searchKeyword);
          } else {
            apiPromise = api.post(api.API.HOUSE_PAGE, queryParams);
          }
          break;
      }
    }

    apiPromise
      .then(res => {
        let houses = [];
        let total = 0;

        if (res && res.code === 200) {
          if (Array.isArray(res.data)) {
            houses = res.data;
            total = res.data.length;
          } else if (res.data && Array.isArray(res.data.records)) {
            houses = res.data.records;
            total = res.data.total || res.data.records.length;
          } else if (res.data && Array.isArray(res.data.list)) {
            houses = res.data.list;
            total = res.data.total || res.data.list.length;
          }
        }

        if (houses.length > 0) {
          let formattedHouses = houseService.formatHouseList(houses);

          console.log('API返回的原始房源数据:', houses.slice(0, 3)); // 显示前3条原始数据
          console.log('格式化后的房源数据:', formattedHouses.slice(0, 3)); // 显示前3条格式化数据

          // 如果是统计筛选，进行前端过滤
          if (this.data.isStatsFilter) {
            console.log('开始统计筛选，类型:', this.data.statsType);
            formattedHouses = this.filterHousesByStats(formattedHouses);
          }

          // 应用筛选条件
          formattedHouses = this.applyFilters(formattedHouses);

          // 应用排序
          formattedHouses = this.applySorting(formattedHouses);

          const newList = refresh ? formattedHouses : [...this.data.houseList, ...formattedHouses];

          this.setData({
            houseList: newList,
            totalCount: formattedHouses.length,
            hasMore: houses.length >= this.data.pageSize,
            loading: false,
            currentPage: this.data.currentPage + 1
          });
        } else {
          this.setData({
            houseList: refresh ? [] : this.data.houseList,
            totalCount: 0,
            hasMore: false,
            loading: false
          });

          if (refresh) {
            wx.showToast({
              title: '暂无房源数据',
              icon: 'none',
              duration: 2000
            });
          }
        }

        if (refresh) {
          wx.stopPullDownRefresh();
        }
      })
      .catch(err => {
        console.error('加载房源列表失败:', err);
        this.setData({ loading: false });
        wx.showToast({
          title: 'API调用失败',
          icon: 'none',
          duration: 2000
        });
        if (refresh) {
          wx.stopPullDownRefresh();
        }
      });
  },

  loadMore: function () {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    this.loadHouseList(false);
  },

  // 刷新列表
  refreshList: function () {
    this.loadHouseList(true);
  },

  // 根据统计类型过滤房源
  filterHousesByStats: function(houses) {
    if (!this.data.isStatsFilter) {
      return houses;
    }

    const statsType = this.data.statsType;
    // 如果没有传递currentTime，使用当前时间
    const currentTimeStr = this.data.currentTime || new Date().toISOString();
    const currentTime = new Date(currentTimeStr);

    console.log('开始过滤房源:', {
      statsType: statsType,
      currentTimeStr: currentTimeStr,
      currentTime: currentTime,
      housesCount: houses.length
    });

    const filteredHouses = houses.filter(house => {
      let result = false;

      switch (statsType) {
        case 'todayNew':
          // 今日新增：根据创建时间筛选
          if (house.createTime) {
            const createDate = new Date(house.createTime);
            const todayStart = new Date(this.data.createTimeStart);
            const todayEnd = new Date(this.data.createTimeEnd + ' 23:59:59');
            result = createDate >= todayStart && createDate <= todayEnd;
          }
          break;

        case 'auctioning':
          // 正在拍卖：开始时间 <= 当前时间 <= 结束时间
          let startTimeValue1 = house.auctionStartTime || house.startTime || house.beginTime || house.auctionTime;
          let endTimeValue = house.auctionEndTime || house.endTime || house.finishTime;

          if (startTimeValue1 && endTimeValue) {
            // 处理开始时间格式
            let startTime;
            if (startTimeValue1.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
              const currentYear = new Date().getFullYear();
              startTime = new Date(`${currentYear}-${startTimeValue1}`);
            } else {
              startTime = new Date(startTimeValue1);
            }

            // 处理结束时间格式
            let endTime;
            if (endTimeValue.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
              const currentYear = new Date().getFullYear();
              endTime = new Date(`${currentYear}-${endTimeValue}`);
            } else {
              endTime = new Date(endTimeValue);
            }

            result = startTime <= currentTime && currentTime <= endTime;
          }
          break;

        case 'upcoming':
          // 即将拍卖：开始时间 > 当前时间
          console.log('检查房源时间字段:', {
            houseId: house.id,
            所有字段: Object.keys(house),
            auctionStartTime: house.auctionStartTime,
            startTime: house.startTime,
            beginTime: house.beginTime,
            auctionTime: house.auctionTime
          });

          // 尝试多个可能的时间字段名
          let startTimeValue = house.auctionStartTime || house.startTime || house.beginTime || house.auctionTime;

          if (startTimeValue) {
            // 处理时间格式，如果是 "08-07 10:00" 格式，需要加上年份
            let startTime;
            if (startTimeValue.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
              // 格式如 "08-07 10:00"，加上当前年份
              const currentYear = new Date().getFullYear();
              const fullTimeStr = `${currentYear}-${startTimeValue}`;
              startTime = new Date(fullTimeStr);
            } else {
              startTime = new Date(startTimeValue);
            }

            result = startTime > currentTime;
            console.log('即将拍卖判断:', {
              houseId: house.id,
              原始时间字段: startTimeValue,
              处理后时间字符串: startTime.toISOString(),
              解析后时间: startTime,
              当前时间: currentTime,
              开始时间大于当前时间: result,
              时间差分钟: (startTime - currentTime) / (1000 * 60)
            });
          } else {
            console.log('房源没有找到开始时间字段:', house.id);
          }
          break;

        case 'avgPrice':
          // 已成交：结束时间 < 当前时间
          let endTimeValue2 = house.auctionEndTime || house.endTime || house.finishTime;

          if (endTimeValue2) {
            // 处理结束时间格式
            let endTime;
            if (endTimeValue2.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
              const currentYear = new Date().getFullYear();
              endTime = new Date(`${currentYear}-${endTimeValue2}`);
            } else {
              endTime = new Date(endTimeValue2);
            }

            result = endTime < currentTime;
          }
          break;

        default:
          result = true;
          break;
      }

      return result;
    });

    console.log('过滤结果:', {
      原始数量: houses.length,
      过滤后数量: filteredHouses.length,
      statsType: statsType
    });

    return filteredHouses;
  },

  // 应用筛选条件
  applyFilters: function(houses) {
    let filteredHouses = houses;

    // 区域筛选 - 从标题中获取
    if (this.data.regionFilter.length > 0) {
      filteredHouses = filteredHouses.filter(house => {
        const title = house.title || '';
        return this.data.regionFilter.some(region => title.includes(region));
      });
    }

    // 面积筛选 - 从建筑面积字段获取
    if (this.data.areaFilter.length > 0) {
      filteredHouses = filteredHouses.filter(house => {
        const area = parseFloat(house.buildingArea) || 0;
        return this.data.areaFilter.some(areaRange => {
          return this.isAreaInRange(area, areaRange);
        });
      });
    }

    // 价格筛选 - 从起拍价获取
    if (this.data.priceFilter.length > 0) {
      filteredHouses = filteredHouses.filter(house => {
        const startPrice = parseFloat(house.startPrice) || 0;
        const priceInWan = startPrice / 10000; // 转换为万元
        return this.data.priceFilter.some(priceRange => {
          return this.isPriceInRange(priceInWan, priceRange);
        });
      });
    }

    // 拍卖方式筛选 - 从标题中获取
    if (this.data.auctionType.length > 0) {
      filteredHouses = filteredHouses.filter(house => {
        const title = house.title || '';
        return this.data.auctionType.some(type => title.includes(type));
      });
    }

    // 拍卖状态筛选 - 从时间判断
    if (this.data.auctionStatus.length > 0) {
      const currentTime = new Date();
      filteredHouses = filteredHouses.filter(house => {
        const status = this.getAuctionStatus(house, currentTime);
        return this.data.auctionStatus.includes(status);
      });
    }

    return filteredHouses;
  },

  // 判断面积是否在范围内
  isAreaInRange: function(area, areaRange) {
    switch (areaRange) {
      case '0-50': return area < 50;
      case '50-70': return area >= 50 && area < 70;
      case '70-90': return area >= 70 && area < 90;
      case '90-120': return area >= 90 && area < 120;
      case '120-150': return area >= 120 && area < 150;
      case '150-200': return area >= 150 && area < 200;
      case '200-300': return area >= 200 && area < 300;
      case '300+': return area >= 300;
      default: return false;
    }
  },

  // 判断价格是否在范围内
  isPriceInRange: function(price, priceRange) {
    switch (priceRange) {
      case '0-50': return price < 50;
      case '50-100': return price >= 50 && price < 100;
      case '100-150': return price >= 100 && price < 150;
      case '150-200': return price >= 150 && price < 200;
      case '200-300': return price >= 200 && price < 300;
      case '300-400': return price >= 300 && price < 400;
      case '400-500': return price >= 400 && price < 500;
      case '500-700': return price >= 500 && price < 700;
      case '700-1000': return price >= 700 && price < 1000;
      case '1000+': return price >= 1000;
      default: return false;
    }
  },

  // 获取拍卖状态
  getAuctionStatus: function(house, currentTime) {
    // 尝试多个可能的时间字段名
    let startTimeValue = house.auctionStartTime || house.startTime || house.beginTime || house.auctionTime;
    let endTimeValue = house.auctionEndTime || house.endTime || house.finishTime;

    if (!startTimeValue) return '未起拍';

    // 处理时间格式
    let startTime;
    if (startTimeValue.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
      const currentYear = new Date().getFullYear();
      startTime = new Date(`${currentYear}-${startTimeValue}`);
    } else {
      startTime = new Date(startTimeValue);
    }

    let endTime;
    if (endTimeValue) {
      if (endTimeValue.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
        const currentYear = new Date().getFullYear();
        endTime = new Date(`${currentYear}-${endTimeValue}`);
      } else {
        endTime = new Date(endTimeValue);
      }
    }

    // 判断状态
    if (currentTime < startTime) {
      return '未起拍';
    } else if (endTime && currentTime > endTime) {
      return '已结束';
    } else {
      return '竞拍中';
    }
  },

  // 应用排序
  applySorting: function(houses) {
    const sortType = this.data.sortType;

    return houses.sort((a, b) => {
      switch (sortType) {
        case 'price-asc':
          return (parseFloat(a.startPrice) || 0) - (parseFloat(b.startPrice) || 0);
        case 'price-desc':
          return (parseFloat(b.startPrice) || 0) - (parseFloat(a.startPrice) || 0);
        case 'unit-price-asc':
          const unitPriceA = (parseFloat(a.startPrice) || 0) / (parseFloat(a.buildingArea) || 1);
          const unitPriceB = (parseFloat(b.startPrice) || 0) / (parseFloat(b.buildingArea) || 1);
          return unitPriceA - unitPriceB;
        case 'unit-price-desc':
          const unitPriceA2 = (parseFloat(a.startPrice) || 0) / (parseFloat(a.buildingArea) || 1);
          const unitPriceB2 = (parseFloat(b.startPrice) || 0) / (parseFloat(b.buildingArea) || 1);
          return unitPriceB2 - unitPriceA2;
        case 'area-desc':
          return (parseFloat(b.buildingArea) || 0) - (parseFloat(a.buildingArea) || 0);
        case 'area-asc':
          return (parseFloat(a.buildingArea) || 0) - (parseFloat(b.buildingArea) || 0);
        case 'latest':
          // 最新发布：按创建时间降序排序
          const createTimeA = new Date(a.createTime || a.publishTime || 0);
          const createTimeB = new Date(b.createTime || b.publishTime || 0);
          return createTimeB - createTimeA;
        case 'time-asc':
          // 按起拍时间排序
          const timeA = this.getAuctionStartTime(a);
          const timeB = this.getAuctionStartTime(b);
          return timeA - timeB;
        case 'smart':
        default:
          // 智能排序：综合考虑时间和价格
          return 0;
      }
    });
  },

  // 获取拍卖开始时间
  getAuctionStartTime: function(house) {
    let startTimeValue = house.auctionStartTime || house.startTime || house.beginTime || house.auctionTime;
    if (!startTimeValue) return new Date(0);

    if (startTimeValue.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
      const currentYear = new Date().getFullYear();
      return new Date(`${currentYear}-${startTimeValue}`);
    } else {
      return new Date(startTimeValue);
    }
  },

  // 阻止弹窗内容区域点击关闭
  preventClose() {
    // 空方法，阻止事件冒泡
  },

  // 隐藏筛选面板的方法
  hideFilterModal() {
    this.setData({
      showFilterModal: false
    });
  },

  hideRegionFilter() {
    this.setData({
      showRegionPanel: false
    });
  },

  hideAreaFilter() {
    this.setData({
      showAreaPanel: false
    });
  },

  hidePriceFilter() {
    this.setData({
      showPricePanel: false
    });
  },

  hideMoreFilter() {
    this.setData({
      showMorePanel: false
    });
  },

  hideSortFilter() {
    this.setData({
      showSortPanel: false
    });
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  // 房源详情页跳转
  onHouseItemTap: function (e) {
    const houseId = e.currentTarget.dataset.id;
    if (houseId) {
      wx.navigateTo({
        url: `/pages/property-detail/property-detail?id=${houseId}`
      });
    }
  },

  // 房源详情页跳转（兼容goToDetail方法名）
  goToDetail: function (e) {
    const houseId = e.currentTarget.dataset.id;
    if (houseId) {
      wx.navigateTo({
        url: `/pages/property-detail/property-detail?id=${houseId}`
      });
    }
  },

  // 筛选功能 - 区域筛选
  showRegionFilter() {
    this.setData({
      showRegionPanel: true,
      tempRegionFilter: [...this.data.regionFilter]
    });
    this.updateRegionActiveStatus();
  },

  /**
   * 更新区域选项
   */
  async updateRegionOptions() {
    const selectedCity = this.data.selectedCity;
    let regionOptions = [];

    // 所有城市都调用API获取区域
    try {
      const response = await houseService.getDistrictsByCity(selectedCity);
      console.log('获取到的区域数据:', response);
      
      if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
        regionOptions = response.data;
      } else {
        // 如果API没有返回数据，使用默认区域选项
        regionOptions = ['市中心', '新区', '开发区', '高新区'];
      }
    } catch (error) {
      console.error('获取区域数据失败:', error);
      // 降级到默认区域选项
      regionOptions = ['市中心', '新区', '开发区', '高新区'];
    }

    this.setData({
      currentRegionOptions: regionOptions
    });
  },

  /**
   * 更新区域选中状态
   */
  updateRegionActiveStatus() {
    const activeStatus = {};
    this.data.currentRegionOptions.forEach(region => {
      activeStatus[region] = this.data.tempRegionFilter.includes(region);
    });
    this.setData({
      regionActiveStatus: activeStatus
    });
  },

  onTempRegionFilterTap(e) {
    const region = e.currentTarget.dataset.region;
    let tempRegionFilter = [...this.data.tempRegionFilter];

    if (region === 'all') {
      tempRegionFilter = [];
    } else {
      const index = tempRegionFilter.indexOf(region);
      if (index > -1) {
        tempRegionFilter.splice(index, 1);
      } else {
        tempRegionFilter.push(region);
      }
    }

    this.setData({
      tempRegionFilter: tempRegionFilter
    });
    this.updateRegionActiveStatus();
  },

  resetRegionFilter() {
    this.setData({
      tempRegionFilter: []
    });
    this.updateRegionActiveStatus();
  },

  confirmRegionFilter() {
    let regionText = '区域';
    if (this.data.tempRegionFilter.length > 0) {
      if (this.data.tempRegionFilter.length === 1) {
        regionText = this.data.tempRegionFilter[0];
      } else {
        regionText = `${this.data.tempRegionFilter.length}项`;
      }
    }

    this.setData({
      regionFilter: [...this.data.tempRegionFilter],
      regionFilterText: regionText,
      showRegionPanel: false
    });
    this.loadHouseList(true);
  },

  // 筛选功能 - 面积筛选
  showAreaFilter() {
    this.setData({
      showAreaPanel: true,
      tempAreaFilter: [...this.data.areaFilter]
    });
    this.updateAreaActiveStatus();
  },

  /**
   * 更新面积选中状态
   */
  updateAreaActiveStatus() {
    const areaOptions = ['0-50', '50-70', '70-90', '90-120', '120-150', '150-200', '200-300', '300+'];
    const activeStatus = {};
    areaOptions.forEach(area => {
      activeStatus[area] = this.data.tempAreaFilter.includes(area);
    });
    this.setData({
      areaActiveStatus: activeStatus
    });
  },

  onTempAreaFilterTap(e) {
    const area = e.currentTarget.dataset.area;
    let tempAreaFilter = [...this.data.tempAreaFilter];

    if (area === 'all') {
      tempAreaFilter = [];
    } else {
      const index = tempAreaFilter.indexOf(area);
      if (index > -1) {
        tempAreaFilter.splice(index, 1);
      } else {
        tempAreaFilter.push(area);
      }
    }

    this.setData({
      tempAreaFilter: tempAreaFilter
    });
    this.updateAreaActiveStatus();
  },

  resetAreaFilter() {
    this.setData({
      tempAreaFilter: []
    });
    this.updateAreaActiveStatus();
  },

  confirmAreaFilter() {
    let areaText = '面积';
    if (this.data.tempAreaFilter.length > 0) {
      if (this.data.tempAreaFilter.length === 1) {
        areaText = this.getAreaText(this.data.tempAreaFilter[0]);
      } else {
        areaText = `${this.data.tempAreaFilter.length}项`;
      }
    }

    this.setData({
      areaFilter: [...this.data.tempAreaFilter],
      areaFilterText: areaText,
      showAreaPanel: false
    });
    this.loadHouseList(true);
  },

  // 筛选功能 - 价格筛选
  showPriceFilter() {
    this.setData({
      showPricePanel: true,
      tempPriceFilter: [...this.data.priceFilter]
    });
    this.updatePriceActiveStatus();
  },

  /**
   * 更新价格选中状态
   */
  updatePriceActiveStatus() {
    const priceOptions = ['0-50', '50-100', '100-150', '150-200', '200-300', '300-400', '400-500', '500-700', '700-1000', '1000+'];
    const activeStatus = {};
    priceOptions.forEach(price => {
      activeStatus[price] = this.data.tempPriceFilter.includes(price);
    });
    this.setData({
      priceActiveStatus: activeStatus
    });
  },

  onTempPriceFilterTap(e) {
    const price = e.currentTarget.dataset.price;
    let tempPriceFilter = [...this.data.tempPriceFilter];

    if (price === 'all') {
      tempPriceFilter = [];
    } else {
      const index = tempPriceFilter.indexOf(price);
      if (index > -1) {
        tempPriceFilter.splice(index, 1);
      } else {
        tempPriceFilter.push(price);
      }
    }

    this.setData({
      tempPriceFilter: tempPriceFilter
    });
    this.updatePriceActiveStatus();
  },

  resetPriceFilter() {
    this.setData({
      tempPriceFilter: [],
      customMinPrice: '',
      customMaxPrice: ''
    });
    this.updatePriceActiveStatus();
  },

  confirmPriceFilter() {
    let priceFilter = [...this.data.tempPriceFilter];
    let priceText = '价格';

    if (this.data.customMinPrice || this.data.customMaxPrice) {
      const min = this.data.customMinPrice || '0';
      const max = this.data.customMaxPrice || '999999';
      priceFilter = [`${min}-${max}`];
      priceText = `${min}-${max}万`;
    } else if (priceFilter.length > 0) {
      if (priceFilter.length === 1) {
        priceText = this.getPriceText(priceFilter[0]);
      } else {
        priceText = `${priceFilter.length}项`;
      }
    }

    this.setData({
      priceFilter: priceFilter,
      priceFilterText: priceText,
      showPricePanel: false
    });
    this.loadHouseList(true);
  },

  // 筛选功能 - 更多筛选
  showMoreFilter() {
    this.setData({
      showMorePanel: true,
      tempAuctionType: [...this.data.auctionType],
      tempAuctionStatus: [...this.data.auctionStatus]
    });
    this.updateAuctionActiveStatus();
  },

  /**
   * 更新拍卖选中状态
   */
  updateAuctionActiveStatus() {
    const auctionTypeOptions = ['一拍', '二拍', '变卖', '其他'];
    const auctionStatusOptions = ['未起拍', '竞拍中', '已成交', '已结束'];

    const typeActiveStatus = {};
    auctionTypeOptions.forEach(type => {
      typeActiveStatus[type] = this.data.tempAuctionType.includes(type);
    });

    const statusActiveStatus = {};
    auctionStatusOptions.forEach(status => {
      statusActiveStatus[status] = this.data.tempAuctionStatus.includes(status);
    });

    this.setData({
      auctionTypeActiveStatus: typeActiveStatus,
      auctionStatusActiveStatus: statusActiveStatus
    });
  },

  onTempAuctionTypeTap(e) {
    const type = e.currentTarget.dataset.type;
    let tempAuctionType = [...this.data.tempAuctionType];

    if (type === 'all') {
      tempAuctionType = [];
    } else {
      const index = tempAuctionType.indexOf(type);
      if (index > -1) {
        tempAuctionType.splice(index, 1);
      } else {
        tempAuctionType.push(type);
      }
    }

    this.setData({
      tempAuctionType: tempAuctionType
    });
    this.updateAuctionActiveStatus();
  },

  onTempAuctionStatusTap(e) {
    const status = e.currentTarget.dataset.status;
    let tempAuctionStatus = [...this.data.tempAuctionStatus];

    if (status === 'all') {
      tempAuctionStatus = [];
    } else {
      const index = tempAuctionStatus.indexOf(status);
      if (index > -1) {
        tempAuctionStatus.splice(index, 1);
      } else {
        tempAuctionStatus.push(status);
      }
    }

    this.setData({
      tempAuctionStatus: tempAuctionStatus
    });
    this.updateAuctionActiveStatus();
  },

  resetMoreFilter() {
    this.setData({
      tempAuctionType: [],
      tempAuctionStatus: []
    });
    this.updateAuctionActiveStatus();
  },

  confirmMoreFilter() {
    this.setData({
      auctionType: [...this.data.tempAuctionType],
      auctionStatus: [...this.data.tempAuctionStatus],
      showMorePanel: false
    });
    this.loadHouseList(true);
  },

  // 筛选功能 - 排序筛选
  showSortFilter() {
    this.setData({
      showSortPanel: true,
      tempSortType: this.data.sortType
    });
  },

  onTempSortTypeTap(e) {
    const sortType = e.currentTarget.dataset.sort;
    this.setData({
      tempSortType: sortType
    });
  },

  resetSortFilter() {
    this.setData({
      tempSortType: 'smart'
    });
  },

  confirmSortFilter() {
    const sortText = this.getSortText(this.data.tempSortType);
    this.setData({
      sortType: this.data.tempSortType,
      sortFilterText: sortText,
      showSortPanel: false
    });
    this.loadHouseList(true);
  },

  // 工具方法
  getAreaText(area) {
    const areaMap = {
      '0-50': '50㎡以下',
      '50-70': '50~70㎡',
      '70-90': '70~90㎡',
      '90-120': '90~120㎡',
      '120-150': '120~150㎡',
      '150-200': '150~200㎡',
      '200-300': '200~300㎡',
      '300+': '300㎡以上'
    };
    return areaMap[area] || area;
  },

  getPriceText(price) {
    const priceMap = {
      '0-50': '50万以下',
      '50-100': '50~100万',
      '100-150': '100~150万',
      '150-200': '150~200万',
      '200-300': '200~300万',
      '300-400': '300~400万',
      '400-500': '400~500万',
      '500-700': '500~700万',
      '700-1000': '700~1000万',
      '1000+': '1000万以上'
    };
    return priceMap[price] || price;
  },

  getSortText(sortType) {
    const sortMap = {
      'smart': '智能排序',
      'latest': '最新发布',
      'price-asc': '总价从低到高',
      'price-desc': '总价从高到低',
      'unit-price-asc': '单价从低到高',
      'unit-price-desc': '单价从高到低',
      'area-desc': '面积从大到小',
      'area-asc': '面积从小到大',
      'time-asc': '起拍时间由近到远'
    };
    return sortMap[sortType] || '智能排序';
  }
});