<!--pages/house-list/house-list.wxml-->
<view class="container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item" bindtap="showRegionFilter">
      <text class="filter-text">{{regionFilterText}}</text>
      <text class="filter-arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="showAreaFilter">
      <text class="filter-text">{{areaFilterText}}</text>
      <text class="filter-arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="showPriceFilter">
      <text class="filter-text">{{priceFilterText}}</text>
      <text class="filter-arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="showMoreFilter">
      <text class="filter-text">更多</text>
      <text class="filter-arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="showSortFilter">
      <text class="filter-text">筛选</text>
      <text class="filter-arrow">▼</text>
    </view>
  </view>

  <!-- 房源列表 -->
  <scroll-view class="house-list" scroll-y="{{true}}" bindscrolltolower="loadMore" enable-back-to-top="{{true}}">
    <view class="house-item" wx:for="{{houseList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <!-- 左侧房源图片 -->
      <view class="house-image-container">
        <image class="house-image" src="{{item.imageUrl}}" mode="aspectFill" lazy-load="{{true}}"></image>
        <!-- 状态标签 -->
        <view class="status-tag" wx:if="{{item.status}}">{{item.status}}</view>
        <!-- 小区名称遮罩层 -->
        <view class="community-overlay" wx:if="{{item.communityName}}">
          <text class="community-name">{{item.communityName}}</text>
        </view>
      </view>

      <!-- 右侧房源信息 -->
      <view class="house-info">
        <!-- 标题和收藏按钮行 -->
        <view class="title-row">
          <view class="auction-status-tag" wx:if="{{item.auctionStatusText}}">{{item.auctionStatusText}}</view>
          <view class="house-title-wrapper">
            <text class="house-title">{{item.title}}</text>
          </view>
        </view>

        <!-- 房源详情 -->
        <view class="house-details">
          <text class="detail-item-area" wx:if="{{item.buildingArea > 0}}">{{item.buildingArea}}㎡</text>
          <text class="detail-item-discountRate" wx:if="{{item.discountRate}}">{{item.discountRate}}</text>
        </view>

        <!-- 价格信息 -->
        <view class="price-row">
          <view class="price-main">
            <text class="price-label">起拍价</text>
            <text class="price-value">{{item.startingPrice}}</text>
            <text class="price-unit">万</text>
          </view>
          <view class="price-market" wx:if="{{item.evaluationPrice}}">
            <text class="market-label">市场价</text>
            <text class="market-value">{{item.evaluationPrice}}万</text>
          </view>
        </view>

        <!-- 拍卖时间 -->
        <view class="auction-time" wx:if="{{item.auctionTime}}">
          <text class="time-text">拍卖时间：{{item.auctionTime}}</text>
        </view>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text" wx:if="{{!loading}}">上拉加载更多</text>
      <view class="loading" wx:if="{{loading}}">
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 没有更多数据提示 -->
    <view class="no-more" wx:if="{{!hasMore && houseList.length > 0}}">
      <text class="no-more-text">没有更多房源了</text>
    </view>
  </scroll-view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{houseList.length === 0 && !loading}}">
    <image class="empty-icon" src="/images/search.png" mode="aspectFit"></image>
    <text class="empty-title">暂无房源</text>
    <text class="empty-desc">暂时没有找到符合条件的房源</text>
    <button class="refresh-btn" bindtap="refreshList">刷新试试</button>
  </view>

  <!-- 筛选弹窗 -->
  <view class="filter-modal" wx:if="{{showFilterModal}}" bindtap="hideFilterModal">
    <view class="filter-content" catchtap="stopPropagation">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <text class="filter-close" bindtap="hideFilterModal">×</text>
      </view>
      
      <view class="filter-body">
        <!-- 价格筛选 -->
        <view class="filter-section">
          <text class="filter-section-title">价格范围</text>
          <view class="filter-options">
            <view class="filter-option {{priceFilter === '' ? 'active' : ''}}" bindtap="selectPriceFilter" data-value="">不限</view>
            <view class="filter-option {{priceFilter === '0-50' ? 'active' : ''}}" bindtap="selectPriceFilter" data-value="0-50">50万以下</view>
            <view class="filter-option {{priceFilter === '50-100' ? 'active' : ''}}" bindtap="selectPriceFilter" data-value="50-100">50-100万</view>
            <view class="filter-option {{priceFilter === '100-200' ? 'active' : ''}}" bindtap="selectPriceFilter" data-value="100-200">100-200万</view>
            <view class="filter-option {{priceFilter === '200+' ? 'active' : ''}}" bindtap="selectPriceFilter" data-value="200+">200万以上</view>
          </view>
        </view>

        <!-- 面积筛选 -->
        <view class="filter-section">
          <text class="filter-section-title">建筑面积</text>
          <view class="filter-options">
            <view class="filter-option {{tempAreaFilter === '' ? 'active' : ''}}" bindtap="selectAreaFilter" data-value="">不限</view>
            <view class="filter-option {{tempAreaFilter === '0-60' ? 'active' : ''}}" bindtap="selectAreaFilter" data-value="0-60">60㎡以下</view>
            <view class="filter-option {{tempAreaFilter === '60-90' ? 'active' : ''}}" bindtap="selectAreaFilter" data-value="60-90">60-90㎡</view>
            <view class="filter-option {{tempAreaFilter === '90-120' ? 'active' : ''}}" bindtap="selectAreaFilter" data-value="90-120">90-120㎡</view>
            <view class="filter-option {{tempAreaFilter === '120+' ? 'active' : ''}}" bindtap="selectAreaFilter" data-value="120+">120㎡以上</view>
          </view>
        </view>
      </view>

      <view class="filter-footer">
        <button class="filter-reset" bindtap="resetFilter">重置</button>
        <button class="filter-confirm" bindtap="confirmFilter">确定</button>
      </view>
    </view>
  </view>

  <!-- 区域筛选面板 -->
  <view class="filter-panel" wx:if="{{showRegionPanel}}" bindtap="hideRegionFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <view class="filter-options-grid">
        <view class="filter-option {{tempRegionFilter.length === 0 ? 'active' : ''}}" bindtap="onTempRegionFilterTap" data-region="all">不限</view>
        <view class="filter-option {{regionActiveStatus[item] ? 'active' : ''}}"
              wx:for="{{currentRegionOptions}}"
              wx:key="*this"
              bindtap="onTempRegionFilterTap"
              data-region="{{item}}">
          {{item}}
        </view>
      </view>
      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetRegionFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmRegionFilter">确定</view>
      </view>
    </view>
  </view>

  <!-- 面积筛选面板 -->
  <view class="filter-panel" wx:if="{{showAreaPanel}}" bindtap="hideAreaFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <view class="filter-options-grid">
        <view class="filter-option {{tempAreaFilter.length === 0 ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="all">不限</view>
        <view class="filter-option {{areaActiveStatus['0-50'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="0-50">50㎡以下</view>
        <view class="filter-option {{areaActiveStatus['50-70'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="50-70">50~70㎡</view>
        <view class="filter-option {{areaActiveStatus['70-90'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="70-90">70~90㎡</view>
        <view class="filter-option {{areaActiveStatus['90-120'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="90-120">90~120㎡</view>
        <view class="filter-option {{areaActiveStatus['120-150'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="120-150">120~150㎡</view>
        <view class="filter-option {{areaActiveStatus['150-200'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="150-200">150~200㎡</view>
        <view class="filter-option {{areaActiveStatus['200-300'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="200-300">200~300㎡</view>
        <view class="filter-option {{areaActiveStatus['300+'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="300+">300㎡以上</view>
      </view>
      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetAreaFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmAreaFilter">确定</view>
      </view>
    </view>
  </view>

  <!-- 价格筛选面板 -->
  <view class="filter-panel" wx:if="{{showPricePanel}}" bindtap="hidePriceFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <view class="filter-options-grid">
        <view class="filter-option {{tempPriceFilter.length === 0 ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="all">不限</view>
        <view class="filter-option {{priceActiveStatus['0-50'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="0-50">50万以下</view>
        <view class="filter-option {{priceActiveStatus['50-100'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="50-100">50~100万</view>
        <view class="filter-option {{priceActiveStatus['100-150'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="100-150">100~150万</view>
        <view class="filter-option {{priceActiveStatus['150-200'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="150-200">150~200万</view>
        <view class="filter-option {{priceActiveStatus['200-300'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="200-300">200~300万</view>
        <view class="filter-option {{priceActiveStatus['300-400'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="300-400">300~400万</view>
        <view class="filter-option {{priceActiveStatus['400-500'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="400-500">400~500万</view>
        <view class="filter-option {{priceActiveStatus['500-700'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="500-700">500~700万</view>
        <view class="filter-option {{priceActiveStatus['700-1000'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="700-1000">700~1000万</view>
        <view class="filter-option {{priceActiveStatus['1000+'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="1000+">1000万以上</view>
      </view>
      <!-- 自定义价格区间 -->
      <view class="custom-price-section">
        <view class="custom-price-title">自定义价格区间</view>
        <view class="custom-price-inputs">
          <input class="price-input" placeholder="最低价" type="number" bindinput="onMinPriceInput" value="{{customMinPrice}}" />
          <text class="price-unit">万</text>
          <text class="price-separator">-</text>
          <input class="price-input" placeholder="最高价" type="number" bindinput="onMaxPriceInput" value="{{customMaxPrice}}" />
          <text class="price-unit">万</text>
        </view>
      </view>
      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetPriceFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmPriceFilter">确定</view>
      </view>
    </view>
  </view>

  <!-- 更多筛选面板 -->
  <view class="filter-panel" wx:if="{{showMorePanel}}" bindtap="hideMoreFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <!-- 拍卖方式 -->
      <view class="filter-section">
        <view class="filter-section-title">拍卖方式</view>
        <view class="filter-options-grid">
          <view class="filter-option {{tempAuctionType.length === 0 ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="all">不限</view>
          <view class="filter-option {{auctionTypeActiveStatus['一拍'] ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="一拍">一拍</view>
          <view class="filter-option {{auctionTypeActiveStatus['二拍'] ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="二拍">二拍</view>
          <view class="filter-option {{auctionTypeActiveStatus['变卖'] ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="变卖">变卖</view>
          <view class="filter-option {{auctionTypeActiveStatus['其他'] ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="其他">其他</view>
        </view>
      </view>

      <!-- 拍卖状态 -->
      <view class="filter-section">
        <view class="filter-section-title">拍卖状态</view>
        <view class="filter-options-grid">
          <view class="filter-option {{tempAuctionStatus.length === 0 ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="all">不限</view>
          <view class="filter-option {{auctionStatusActiveStatus['未起拍'] ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="未起拍">未起拍</view>
          <view class="filter-option {{auctionStatusActiveStatus['竞拍中'] ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="竞拍中">竞拍中</view>
          <view class="filter-option {{auctionStatusActiveStatus['已成交'] ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="已成交">已成交</view>
          <view class="filter-option {{auctionStatusActiveStatus['已结束'] ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="已结束">已结束</view>
        </view>
      </view>

      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetMoreFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmMoreFilter">确定</view>
      </view>
    </view>
  </view>

  <!-- 排序筛选面板 -->
  <view class="filter-panel" wx:if="{{showSortPanel}}" bindtap="hideSortFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <view class="sort-options">
        <view class="sort-option {{tempSortType === 'smart' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="smart">
          <text class="sort-text">智能排序</text>
        </view>
        <view class="sort-option {{tempSortType === 'latest' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="latest">
          <text class="sort-text">最新发布</text>
        </view>
        <view class="sort-option {{tempSortType === 'price-asc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="price-asc">
          <text class="sort-text">总价从低到高</text>
        </view>
        <view class="sort-option {{tempSortType === 'price-desc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="price-desc">
          <text class="sort-text">总价从高到低</text>
        </view>
        <view class="sort-option {{tempSortType === 'unit-price-asc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="unit-price-asc">
          <text class="sort-text">单价从低到高</text>
        </view>
        <view class="sort-option {{tempSortType === 'unit-price-desc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="unit-price-desc">
          <text class="sort-text">单价从高到低</text>
        </view>
        <view class="sort-option {{tempSortType === 'area-desc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="area-desc">
          <text class="sort-text">面积从大到小</text>
        </view>
        <view class="sort-option {{tempSortType === 'area-asc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="area-asc">
          <text class="sort-text">面积从小到大</text>
        </view>
        <view class="sort-option {{tempSortType === 'time-asc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="time-asc">
          <text class="sort-text">起拍时间由近到远</text>
        </view>
      </view>

      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetSortFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmSortFilter">确定</view>
      </view>
    </view>
  </view>
</view>
