// pages/consultation-records/consultation-records.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const util = require('../../utils/util.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    recordList: [],
    loading: true,
    error: false,
    errorMessage: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('咨询记录页面加载')
    this.checkLoginAndLoadRecords()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载数据
    this.checkLoginAndLoadRecords()
  },

  /**
   * 检查登录状态并加载记录
   */
  checkLoginAndLoadRecords() {
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      this.setData({
        loading: false,
        error: true,
        errorMessage: '请先登录后查看咨询记录'
      })
      
      // 延迟显示登录提示
      setTimeout(() => {
        wx.showModal({
          title: '需要登录',
          content: '查看咨询记录需要先登录，是否现在去登录？',
          showCancel: true,
          cancelText: '取消',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({
                url: '/pages/profile/profile'
              })
            } else {
              wx.navigateBack()
            }
          }
        })
      }, 500)
      return
    }

    // 已登录，加载咨询记录
    this.loadRecords()
  },

  /**
   * 加载咨询记录
   */
  async loadRecords() {
    try {
      this.setData({
        loading: true,
        error: false,
        errorMessage: ''
      })

      // 获取当前用户信息
      const currentUser = userService.getLocalUserInfo()
      if (!currentUser || !currentUser.id) {
        throw new Error('获取用户信息失败')
      }

      // 获取用户token
      const accessToken = wx.getStorageSync('accessToken')
      if (!accessToken) {
        throw new Error('用户未登录')
      }

      console.log('开始加载咨询记录，用户ID:', currentUser.id)

      // 调用查询咨询记录API
      const response = await api.get(api.API.CONSULTATION_USER, 
        { userId: currentUser.id }, 
        { 'Authorization': `Bearer ${accessToken}` }
      )

      console.log('咨询记录API响应:', response)

      if (response.code === 200) {
        // 处理时间格式
        const recordList = (response.data || []).map(item => ({
          ...item,
          createTime: this.formatTime(item.createTime)
        }))

        this.setData({
          recordList: recordList,
          loading: false,
          error: false
        })

        console.log('咨询记录加载成功，共', recordList.length, '条记录')
      } else {
        throw new Error(response.message || '获取咨询记录失败')
      }

    } catch (error) {
      console.error('加载咨询记录失败:', error)
      
      this.setData({
        loading: false,
        error: true,
        recordList: []
      })

      const errorMessage = error.message || error.errMsg || '加载失败'
      if (errorMessage.includes('登录') || errorMessage.includes('401')) {
        this.setData({
          errorMessage: '登录已过期，请重新登录'
        })
      } else if (errorMessage.includes('网络')) {
        this.setData({
          errorMessage: '网络连接失败，请检查网络'
        })
      } else {
        this.setData({
          errorMessage: '加载失败，请重试'
        })
      }
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '暂无时间'
    
    try {
      const date = new Date(timeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      console.error('时间格式化失败:', error)
      return timeStr
    }
  },

  /**
   * 跳转到咨询页面
   */
  goToConsultation() {
    wx.navigateTo({
      url: '/pages/cooperation/cooperation'
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新咨询记录')
    this.loadRecords().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onReachBottom() {
    // 暂时不实现分页加载
    console.log('到达页面底部')
  }

})
