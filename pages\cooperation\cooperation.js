// pages/cooperation/cooperation.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const util = require('../../utils/util.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 性别选项
    genderOptions: ['先生', '女士'],
    genderIndex: 0,

    // 合作类型选项
    cooperationTypes: [
      '房产代理合作',
      '投资合作',
      '技术合作',
      '品牌合作',
      '渠道合作',
      '其他合作'
    ],
    cooperationTypeIndex: 0,

    // 表单数据
    consultCity: '',
    contactName: '',
    contactPhone: '',
    consultContent: '',
    remarks: '',

    // 提交状态
    canSubmit: false,
    isSubmitting: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('合作咨询页面加载');
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态（仅在页面加载时提示）
   */
  checkLoginStatus() {
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      // 延迟显示，避免页面加载时立即弹窗
      setTimeout(() => {
        wx.showModal({
          title: '温馨提示',
          content: '提交合作咨询需要先登录，是否现在去登录？',
          showCancel: true,
          cancelText: '稍后登录',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              // 跳转到个人中心页面进行登录
              wx.switchTab({
                url: '/pages/profile/profile'
              })
            }
            // 用户选择稍后登录，可以继续填写表单
          }
        })
      }, 500)
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      // 如果未登录，不显示弹窗，只在提交时提示
      console.log('用户未登录，请在提交时登录')
    }
    this.checkCanSubmit();
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      genderIndex: index
    });
    this.checkCanSubmit();
  },

  /**
   * 合作类型选择
   */
  onCooperationTypeChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      cooperationTypeIndex: index
    });
    this.checkCanSubmit();
  },

  /**
   * 咨询城市输入
   */
  onConsultCityInput(e) {
    const value = e.detail.value.trim();
    this.setData({
      consultCity: value
    });
    this.checkCanSubmit();
  },

  /**
   * 联系人姓名输入
   */
  onContactNameInput(e) {
    const value = this.validateNameInput(e.detail.value);
    this.setData({
      contactName: value
    });
    this.checkCanSubmit();
  },

  /**
   * 联系电话输入
   */
  onContactPhoneInput(e) {
    const value = this.validatePhoneInput(e.detail.value);
    this.setData({
      contactPhone: value
    });
    this.checkCanSubmit();
  },

  /**
   * 咨询内容输入
   */
  onConsultContentInput(e) {
    const value = e.detail.value.trim();
    this.setData({
      consultContent: value
    });
    this.checkCanSubmit();
  },

  /**
   * 备注输入
   */
  onRemarksInput(e) {
    const value = e.detail.value.trim();
    this.setData({
      remarks: value
    });
  },

  /**
   * 验证姓名输入
   */
  validateNameInput(value) {
    // 移除特殊字符，只保留中文、英文和空格
    return value.replace(/[^\u4e00-\u9fa5a-zA-Z\s]/g, '');
  },

  /**
   * 验证手机号输入
   */
  validatePhoneInput(value) {
    // 只允许数字
    return value.replace(/[^\d]/g, '');
  },

  /**
   * 验证手机号格式
   */
  validatePhoneFormat(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { consultCity, contactName, contactPhone, consultContent } = this.data;

    const canSubmit = consultCity.trim() !== '' &&
                     contactName.trim() !== '' &&
                     contactPhone.trim() !== '' &&
                     this.validatePhoneFormat(contactPhone) &&
                     consultContent.trim() !== '' &&
                     consultContent.trim().length >= 10; // 至少10个字符

    this.setData({
      canSubmit: canSubmit
    });
  },

  /**
   * 查看咨询记录
   */
  viewConsultRecord() {
    // 检查登录状态
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      util.showError('请先登录后查看咨询记录')
      return
    }

    // 跳转到咨询记录页面
    wx.navigateTo({
      url: '/pages/consultation-records/consultation-records'
    })
  },

  /**
   * 提交表单
   */
  onSubmit() {

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.data.contactPhone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    // 获取当前用户信息
    const currentUser = userService.getLocalUserInfo()
    if (!currentUser || !currentUser.id) {
      util.showError('请先登录')
      return
    }

    // 构建提交数据（按照API接口要求）
    const submitData = {
      userId: currentUser.id,
      city: this.data.consultCity,
      name: this.data.contactName,
      gender: this.data.genderOptions[this.data.genderIndex],
      contactInfo: this.data.contactPhone,
      consultationContent: this.data.consultContent,
      remarks: this.data.remarks || ''
    };

    console.log('提交数据:', submitData);

    // 调用合作咨询API
    this.submitConsultation(submitData);
  },

  /**
   * 提交合作咨询
   */
  async submitConsultation(submitData) {
    try {
      util.showLoading('提交中...')

      // 获取用户token
      const accessToken = wx.getStorageSync('accessToken')
      if (!accessToken) {
        util.hideLoading()
        util.showError('请先登录')
        return
      }

      // 调用合作咨询API
      const response = await api.post(api.API.CONSULTATION_CREATE, submitData, {
        'Authorization': `Bearer ${accessToken}`
      })

      util.hideLoading()

      if (response.code === 200) {
        // 提交成功
        console.log('合作咨询提交成功:', response.data)

        wx.showModal({
          title: '提交成功',
          content: `您的合作咨询已提交成功，咨询ID：${response.data.id}，我们会尽快联系您！`,
          showCancel: false,
          confirmText: '确定',
          success: (res) => {
            if (res.confirm) {
              // 清空表单
              this.resetForm()
              // 返回上一页
              wx.navigateBack()
            }
          }
        })
      } else {
        // 提交失败
        util.showError(response.message || '提交失败，请重试')
      }

    } catch (error) {
      util.hideLoading()
      console.error('提交合作咨询失败:', error)

      const errorMessage = error.message || error.errMsg || '提交失败'
      if (errorMessage.includes('登录') || errorMessage.includes('401')) {
        util.showError('登录已过期，请重新登录')
      } else if (errorMessage.includes('网络')) {
        util.showError('网络连接失败，请检查网络')
      } else {
        util.showError('提交失败，请重试')
      }
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      genderIndex: 0,
      consultCity: '',
      contactName: '',
      contactPhone: '',
      consultContent: '',
      remarks: '',
      canSubmit: false
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '锦绣资产 - 合作咨询',
      path: '/pages/cooperation/cooperation'
    };
  }
});
