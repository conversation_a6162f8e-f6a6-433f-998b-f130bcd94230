/**
 * 工具函数库
 */

/**
 * 格式化时间
 */
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

/**
 * 显示加载提示
 */
const showLoading = (title = '加载中...') => {
  wx.showLoading({
    title: title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
const hideLoading = () => {
  wx.hideLoading()
}

/**
 * 显示成功提示
 */
const showSuccess = (title = '操作成功') => {
  wx.showToast({
    title: title,
    icon: 'success',
    duration: 2000
  })
}

/**
 * 显示错误提示
 */
const showError = (title = '操作失败') => {
  wx.showToast({
    title: title,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 格式化数字，添加千分位分隔符
 */
const formatNumber2 = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 格式化价格
 */
const formatPrice = (price) => {
  if (price >= 10000) {
    return (price / 10000).toFixed(1) + '万'
  }
  return formatNumber2(price)
}

/**
 * 防抖函数
 */
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 获取当前页面路径
 */
const getCurrentPagePath = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage.route
}

/**
 * 页面跳转封装
 */
const navigateTo = (url) => {
  wx.navigateTo({
    url: url,
    fail: (err) => {
      console.error('页面跳转失败:', err)
      showError('页面跳转失败')
    }
  })
}

/**
 * 页面重定向封装
 */
const redirectTo = (url) => {
  wx.redirectTo({
    url: url,
    fail: (err) => {
      console.error('页面重定向失败:', err)
      showError('页面跳转失败')
    }
  })
}

/**
 * 返回上一页
 */
const navigateBack = (delta = 1) => {
  wx.navigateBack({
    delta: delta,
    fail: (err) => {
      console.error('返回失败:', err)
    }
  })
}

module.exports = {
  formatTime,
  showLoading,
  hideLoading,
  showSuccess,
  showError,
  formatNumber: formatNumber2,
  formatPrice,
  debounce,
  throttle,
  getCurrentPagePath,
  navigateTo,
  redirectTo,
  navigateBack
}
