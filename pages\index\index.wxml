<!--pages/index/index.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <!-- 左侧位置选择 -->
      <view class="location-selector" bindtap="onLocationSelect">
        <image class="location-icon" src="../../images/postion.png"></image>
        <text class="location-text">{{selectedCity}}</text>
      </view>

      <!-- 中间标题 -->
      <view class="navbar-title">锦绣资产</view>

      <!-- 右侧占位 -->
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="carousel-section">
    <swiper class="carousel-swiper" indicator-dots="{{carousels.length > 1}}" autoplay="{{carousels.length > 1}}" interval="3000" duration="500" circular="{{carousels.length > 1}}" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#ffffff">
      <swiper-item wx:for="{{carousels}}" wx:key="id" class="carousel-item">
        <image class="carousel-image" src="{{item.imageUrl}}" mode="aspectFill" bindtap="onCarouselTap" data-carousel="{{item}}" lazy-load="{{true}}" />
      </swiper-item>
    </swiper>
  </view>

  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-box" style="height: 70rpx; display: flex; box-sizing: border-box; position: relative">
      <image class="search-icon" src="../../images/search.png" style="width: 40rpx; height: 40rpx; display: block; box-sizing: border-box; position: relative; left: 20rpx; top: 2rpx"></image>
      <input class="search-input" placeholder="按楼盘名/关键字搜索" placeholder-style="color: #999;" bindinput="onSearchInput" bindconfirm="onSearchConfirm" value="{{searchValue}}" />
    </view>
    <!-- <view class="location-btn" bindtap="onLocationTap" style="position: relative; left: -6rpx; top: -102rpx">
      <image class="location-icon" src="../../images/location.png" style="width: 175rpx; height: 77rpx; display: block; box-sizing: border-box"></image>
    </view> -->
  </view>

  <!-- 功能菜单网格 -->
  <view class="big-view">
    <!-- 横向排列的法拍相关菜单（与之前菜单区分） -->
    <view class="menu-row-horizontal">
      <view class="menu-item-horizontal {{filterType === 'house' ? 'active' : ''}}" bindtap="onHouseTypeFilter" data-type="house">
        <view class="menu-icon-horizontal">
          <image src="../../images/auction.png" mode="widthFix"></image>
        </view>
        <text class="menu-text-horizontal">法拍住宅</text>
      </view>

      <view class="menu-item-horizontal {{filterType === 'commercial' ? 'active' : ''}}" bindtap="onHouseTypeFilter" data-type="commercial">
        <view class="menu-icon-horizontal">
          <image src="../../images/commercial.png" mode="widthFix"></image>
        </view>
        <text class="menu-text-horizontal">法拍商办</text>
      </view>

      <view class="menu-item-horizontal {{filterType === 'special' ? 'active' : ''}}" bindtap="onHouseTypeFilter" data-type="special">
        <view class="menu-icon-horizontal">
          <image src="../../images/special.png" mode="widthFix"></image>
        </view>
        <text class="menu-text-horizontal">特殊资产</text>
      </view>

      <view class="menu-item-horizontal {{filterType === 'premium' ? 'active' : ''}}" bindtap="onHouseTypeFilter" data-type="premium">
        <view class="menu-icon-horizontal">
          <image src="../../images/premium.png" mode="widthFix"></image>
        </view>
        <text class="menu-text-horizontal">精选房源</text>
      </view>
    </view>

    <!-- 外层容器统一样式 -->
    <view class="actpo">
      <!-- 第一行菜单 -->
      <view class="menu-row">
        <view class="menu-item" bindtap="showBuyHouseModal">
          <view class="menu-icon">
            <image src="../../images/buy.png" mode="widthFix" class="icon-img"></image>
          </view>
          <text class="menu-tex">我要买房</text>
        </view>

        <view class="menu-item" bindtap="navigateTo" data-page="sell-house">
          <view class="menu-icon">
            <image src="../../images/sell.png" mode="widthFix" class="icon-img"></image>
          </view>
          <text class="menu-tex">我要卖房</text>
        </view>

        <view class="menu-item" bindtap="navigateTo" data-page="service">
          <view class="menu-icon">
            <image src="../../images/service.png" mode="widthFix" class="icon-img"></image>
          </view>
          <text class="menu-tex">服务流程</text>
        </view>

        <view class="menu-item" bindtap="navigateTo" data-page="cooperation">
          <view class="menu-icon">
            <image src="../../images/cooperation.png" mode="widthFix" class="icon-img"></image>
          </view>
          <text class="menu-tex">合作咨询</text>
        </view>
      </view>

      <!-- 第二行菜单 -->
      <view class="menu-row">
        <view class="menu-item" bindtap="navigateTo" data-page="calculator">
          <view class="menu-icon">
            <image src="../../images/calculator.png" mode="widthFix" class="icon-img"></image>
          </view>
          <text class="menu-tex">房贷计算</text>
        </view>

        <view class="menu-item" bindtap="navigateTo" data-page="map">
          <view class="menu-icon">
            <image src="../../images/map.png" mode="widthFix" class="icon-img"></image>
          </view>
          <text class="menu-tex">地图找房</text>
        </view>

        <view class="menu-item" bindtap="navigateTo" data-page="live">
          <view class="menu-icon">
            <image src="../../images/live.png" mode="widthFix" class="icon-img"></image>
          </view>
          <text class="menu-tex">直播看房</text>
        </view>

        <view class="menu-item" bindtap="navigateTo" data-page="transfer">
          <view class="menu-icon">
            <image src="../../images/transfer.png" mode="widthFix" class="icon-img"></image>
          </view>
          <text class="menu-tex">装修服务</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据统计卡片 -->
  <view class="stats-section">
    <view class="card-grid">
      <view class="card-item" bindtap="onStatsCardTap" data-type="todayNew">
        <text style="z-index:10; position: absolute; top: 50rpx; left:40rpx;">{{statsData.todayNew}}套</text>
        <text style="z-index:10; position: absolute; top: 90rpx; left:40rpx;font-size: 22rpx;">今日新增 ></text>
        <image src="../../images/aaa.png" mode="aspectFit" class="card-image"></image>
      </view>
      <view class="card-item" bindtap="onStatsCardTap" data-type="auctioning">
        <text style="z-index:10; position: absolute; top: 50rpx; left:40rpx;">{{statsData.auctioning}}套</text>
        <text style="z-index:10; position: absolute; top: 90rpx; left:40rpx;font-size: 22rpx;">正在拍卖 ></text>
        <image src="../../images/aab.png" mode="aspectFit" class="card-image"></image>
      </view>
      <view class="card-item" bindtap="onStatsCardTap" data-type="upcoming">
        <text style="z-index:10; position: absolute; top: 50rpx; left:40rpx;">{{statsData.upcoming}}套</text>
        <text style="z-index:10; position: absolute; top: 90rpx; left:40rpx;font-size: 22rpx;">即将拍卖 ></text>
        <image src="../../images/aac.png" mode="aspectFit" class="card-image"></image>
      </view>
      <view class="card-item" data-type="avgPrice">
        <text style="z-index:10; position: absolute; top: 50rpx; left:40rpx;">{{statsData.avgPrice || 0}}元</text>
        <text style="z-index:10; position: absolute; top: 90rpx; left:40rpx;font-size: 22rpx;">成交均价</text>
        <image src="../../images/aad.png" mode="aspectFit" class="card-image"></image>
      </view>
    </view>
  </view>

  <!-- 房源列表模块 -->
  <view class="house-list-section">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" bindtap="showRegionFilter">
        <text class="filter-text">{{regionFilterText}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showAreaFilter">
        <text class="filter-text">{{areaFilterText}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showPriceFilter">
        <text class="filter-text">{{priceFilterText}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showMoreFilter">
        <text class="filter-text">更多</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showSortFilter">
        <text class="filter-text">筛选</text>
        <text class="filter-arrow">▼</text>
      </view>
    </view>

    <!-- 房源列表 -->
    <scroll-view class="house-list" scroll-y="{{true}}" bindscrolltolower="onReachBottom" enable-back-to-top="{{true}}">
      <view class="house-item" wx:for="{{houseList}}" wx:key="id" bindtap="onHouseItemTap" data-house="{{item}}">
        <!-- 左侧房源图片 -->
        <view class="house-image-container">
          <image class="house-image" src="{{item.imageUrl}}" mode="aspectFill" lazy-load="{{true}}"></image>
          <!-- 状态标签 -->
          <view class="status-tag" wx:if="{{item.status}}">{{item.status}}</view>
          <!-- 小区名称遮罩层 -->
          <view class="community-overlay" wx:if="{{item.communityName}}">
            <text class="community-name">{{item.communityName}}</text>
          </view>
        </view>

        <!-- 右侧房源信息 -->
        <view class="house-info">
          <!-- 标题行 -->
          <view class="title-row">
            <view class="auction-status-tag" wx:if="{{item.auctionStatusText}}">{{item.auctionStatusText}}</view>
            <view class="house-title-wrapper">
              <text class="house-title">{{item.title}}</text>
            </view>
          </view>

          <!-- 房源详情 -->
          <view class="house-details">
            <text class="detail-item-area" wx:if="{{item.buildingArea > 0}}">{{item.buildingArea}}㎡</text>
            <text class="detail-item-discountRate" wx:if="{{item.discountRate}}">{{item.discountRate}}</text>
          </view>

          <!-- 价格信息 -->
          <view class="price-row">
            <view class="price-main">
              <text class="price-label">起拍价</text>
              <text class="price-value">{{item.startingPrice}}</text>
              <text class="price-unit">万</text>
            </view>
            <view class="price-market" wx:if="{{item.evaluationPrice}}">
              <text class="market-label">市场价</text>
              <text class="market-value">{{item.evaluationPrice}}万</text>
            </view>
          </view>

          <!-- 拍卖时间 -->
          <view class="auction-time" wx:if="{{item.auctionTime}}">
            <text class="time-text">拍卖时间：{{item.auctionTime}}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text wx:if="{{!loading}}">上拉加载更多</text>
      <text wx:if="{{loading}}">加载中...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && houseList.length > 0}}">
      <text>没有更多数据了</text>
    </view>
  </view>

  <!-- 我要买房半屏弹窗 -->
  <view class="modal-overlay" wx:if="{{showBuyHouseModal}}" bindtap="hideBuyHouseModal">
    <view class="modal-container" catchtap="preventClose">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <view class="modal-title"></view>
        <view class="modal-close" bindtap="hideBuyHouseModal">
        </view>
      </view>

      <!-- 弹窗内容 -->
      <view class="modal-content">
        <view class="login-options">
          <!-- 手机号快捷登录选项1 -->
          <view class="login-option" bindtap="onPhoneLogin1">
            <view class="login-icon">
              <image src="../../images/phone-log.png" class="option-icon"></image>
            </view>
            <text class="login-text">手机号快捷登录</text>
          </view>

          <!-- 手机号快捷登录选项2 -->
          <view class="login-option" bindtap="onPhoneLogin2">
            <view class="login-icon">
              <image src="../../images/phone-log2.png" class="option-icon"></image>
            </view>
            <text class="login-text">手机号快捷登录</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 区域筛选面板 -->
  <view class="filter-panel" wx:if="{{showRegionPanel}}" bindtap="hideRegionFilter">
    <view class="filter-panel-content" catchtap="preventClose">

      <view class="filter-options-grid">
        <view class="filter-option {{tempRegionFilter.length === 0 ? 'active' : ''}}" bindtap="onTempRegionFilterTap" data-region="all">不限</view>
        <view class="filter-option {{regionActiveStatus[item] ? 'active' : ''}}" wx:for="{{currentRegionOptions}}" wx:key="*this" bindtap="onTempRegionFilterTap" data-region="{{item}}">
          {{item}}
        </view>
      </view>
      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetRegionFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmRegionFilter">确定</view>
      </view>
    </view>
  </view>

  <!-- 面积筛选面板 -->
  <view class="filter-panel" wx:if="{{showAreaPanel}}" bindtap="hideAreaFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <view class="filter-options-grid">
        <view class="filter-option {{tempAreaFilter.length === 0 ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="all">不限</view>
        <view class="filter-option {{areaActiveStatus['0-50'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="0-50">50㎡以下</view>
        <view class="filter-option {{areaActiveStatus['50-70'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="50-70">50~70㎡</view>
        <view class="filter-option {{areaActiveStatus['70-90'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="70-90">70~90㎡</view>
        <view class="filter-option {{areaActiveStatus['90-120'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="90-120">90~120㎡</view>
        <view class="filter-option {{areaActiveStatus['120-150'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="120-150">120~150㎡</view>
        <view class="filter-option {{areaActiveStatus['150-200'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="150-200">150~200㎡</view>
        <view class="filter-option {{areaActiveStatus['200-300'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="200-300">200~300㎡</view>
        <view class="filter-option {{areaActiveStatus['300+'] ? 'active' : ''}}" bindtap="onTempAreaFilterTap" data-area="300+">300㎡以上</view>
      </view>
      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetAreaFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmAreaFilter">确定</view>
      </view>
    </view>
  </view>

  <!-- 价格筛选面板 -->
  <view class="filter-panel" wx:if="{{showPricePanel}}" bindtap="hidePriceFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <view class="filter-options-grid">
        <view class="filter-option {{tempPriceFilter.length === 0 ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="all">不限</view>
        <view class="filter-option {{priceActiveStatus['0-50'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="0-50">50万以下</view>
        <view class="filter-option {{priceActiveStatus['50-100'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="50-100">50~100万</view>
        <view class="filter-option {{priceActiveStatus['100-150'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="100-150">100~150万</view>
        <view class="filter-option {{priceActiveStatus['150-200'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="150-200">150~200万</view>
        <view class="filter-option {{priceActiveStatus['200-300'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="200-300">200~300万</view>
        <view class="filter-option {{priceActiveStatus['300-400'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="300-400">300~400万</view>
        <view class="filter-option {{priceActiveStatus['400-500'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="400-500">400~500万</view>
        <view class="filter-option {{priceActiveStatus['500-700'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="500-700">500~700万</view>
        <view class="filter-option {{priceActiveStatus['700-1000'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="700-1000">700~1000万</view>
        <view class="filter-option {{priceActiveStatus['1000+'] ? 'active' : ''}}" bindtap="onTempPriceFilterTap" data-price="1000+">1000万以上</view>
      </view>
      <!-- 自定义价格区间 -->
      <view class="custom-price-section">
        <view class="custom-price-title">自定义价格区间</view>
        <view class="custom-price-inputs">
          <input class="price-input" placeholder="最低价" type="number" bindinput="onMinPriceInput" value="{{customMinPrice}}" />
          <text class="price-separator">-</text>
          <input class="price-input" placeholder="最高价" type="number" bindinput="onMaxPriceInput" value="{{customMaxPrice}}" />
          <text class="price-unit">万</text>
        </view>
      </view>
      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetPriceFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmPriceFilter">确定</view>
      </view>
    </view>
  </view>

  <!-- 更多筛选面板 -->
  <view class="filter-panel" wx:if="{{showMorePanel}}" bindtap="hideMoreFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <!-- 拍卖方式 -->
      <view class="filter-section">
        <view class="filter-section-title">拍卖方式</view>
        <view class="filter-options-grid">
          <view class="filter-option {{tempAuctionType.length === 0 ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="all">不限</view>
          <view class="filter-option {{auctionTypeActiveStatus['一拍'] ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="一拍">一拍</view>
          <view class="filter-option {{auctionTypeActiveStatus['二拍'] ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="二拍">二拍</view>
          <view class="filter-option {{auctionTypeActiveStatus['变卖'] ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="变卖">变卖</view>
          <view class="filter-option {{auctionTypeActiveStatus['其他'] ? 'active' : ''}}" bindtap="onTempAuctionTypeTap" data-type="其他">其他</view>
        </view>
      </view>

      <!-- 拍卖状态 -->
      <view class="filter-section">
        <view class="filter-section-title">拍卖状态</view>
        <view class="filter-options-grid">
          <view class="filter-option {{tempAuctionStatus.length === 0 ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="all">不限</view>
          <view class="filter-option {{auctionStatusActiveStatus['未起拍'] ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="未起拍">未起拍</view>
          <view class="filter-option {{auctionStatusActiveStatus['竞拍中'] ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="竞拍中">竞拍中</view>
          <view class="filter-option {{auctionStatusActiveStatus['已成交'] ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="已成交">已成交</view>
          <view class="filter-option {{auctionStatusActiveStatus['已结束'] ? 'active' : ''}}" bindtap="onTempAuctionStatusTap" data-status="已结束">已结束</view>
        </view>
      </view>

      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetMoreFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmMoreFilter">确定</view>
      </view>
    </view>
  </view>

  <!-- 排序筛选面板 -->
  <view class="filter-panel" wx:if="{{showSortPanel}}" bindtap="hideSortFilter">
    <view class="filter-panel-content" catchtap="preventClose">
      <view class="sort-options">
        <view class="sort-option {{tempSortType === 'smart' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="smart">
          <text class="sort-text">智能排序</text>
        </view>
        <view class="sort-option {{tempSortType === 'latest' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="latest">
          <text class="sort-text">最新发布</text>
        </view>
        <view class="sort-option {{tempSortType === 'price-asc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="price-asc">
          <text class="sort-text">总价从低到高</text>
        </view>
        <view class="sort-option {{tempSortType === 'price-desc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="price-desc">
          <text class="sort-text">总价从高到低</text>
        </view>
        <view class="sort-option {{tempSortType === 'unit-price-asc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="unit-price-asc">
          <text class="sort-text">单价从低到高</text>
        </view>
        <view class="sort-option {{tempSortType === 'unit-price-desc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="unit-price-desc">
          <text class="sort-text">单价从高到低</text>
        </view>
        <view class="sort-option {{tempSortType === 'area-desc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="area-desc">
          <text class="sort-text">面积从大到小</text>
        </view>
        <view class="sort-option {{tempSortType === 'area-asc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="area-asc">
          <text class="sort-text">面积从小到大</text>
        </view>
        <view class="sort-option {{tempSortType === 'time-asc' ? 'active' : ''}}" bindtap="onTempSortTypeTap" data-sort="time-asc">
          <text class="sort-text">起拍时间由近到远</text>
        </view>
      </view>

      <view class="filter-panel-footer">
        <view class="filter-reset-btn" bindtap="resetSortFilter">
          <image class="reset-icon" src="../../images/reset.png"></image>
        </view>
        <view class="filter-confirm-btn" bindtap="confirmSortFilter">确定</view>
      </view>
    </view>
  </view>
</view>