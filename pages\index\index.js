// pages/index/index.js
const util = require('../../utils/util.js')
const api = require('../../config/api.js')
const carouselService = require('../../services/carousel.js')
const houseService = require('../../services/house.js')
const cityService = require('../../services/city.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    searchValue: '',
    showBuyHouseModal: false, // 控制我要买房弹窗显示
    carousels: [], // 轮播图数据
    statsData: {
      todayNew: 86,
      auctioning: 116,
      upcoming: 2068,
      avgPrice: 7650000 // 765万元（以元为单位）
    },
    // 导航栏相关
    statusBarHeight: 0,
    navBarHeight: 88,
    selectedCity: '重庆市区',
    // 房源列表相关
    houseList: [],
    filterType: 'all',
    currentPage: 1,
    pageSize: 20,
    hasMore: true,
    loading: false,
    // 筛选面板显示状态
    showRegionPanel: false,
    showAreaPanel: false,
    showPricePanel: false,
    showMorePanel: false,
    showSortPanel: false,
    // 当前筛选条件（支持多选）
    regionFilter: [],
    areaFilter: [],
    priceFilter: [],
    auctionType: [],
    auctionStatus: [],
    sortType: 'smart',
    customMinPrice: '',
    customMaxPrice: '',
    // 临时筛选条件（用于面板中的选择）
    tempRegionFilter: [],
    tempAreaFilter: [],
    tempPriceFilter: [],
    tempAuctionType: [],
    tempAuctionStatus: [],
    tempSortType: 'smart',
    // 筛选文本显示
    regionFilterText: '区域',
    areaFilterText: '面积',
    priceFilterText: '价格',
    sortFilterText: '智能排序',
    // 当前城市对应的区域选项
    currentRegionOptions: [],
    // 选中状态
    regionActiveStatus: {},
    areaActiveStatus: {},
    priceActiveStatus: {},
    auctionTypeActiveStatus: {},
    auctionStatusActiveStatus: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initNavBar();
    this.loadStatsData();
    this.loadCarousels();
    this.loadHouseList();
    this.loadSelectedCity();
    this.updateRegionOptions();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 从城市选择页面返回时更新选中的城市
    const oldCityRaw = wx.getStorageSync('selectedCity') || '重庆市区'
    const oldCity = cityService.formatCityDisplay(oldCityRaw)
    console.log('onShow - 旧城市:', oldCityRaw, '格式化:', oldCity)

    this.loadSelectedCity();

    const newCityRaw = wx.getStorageSync('selectedCity') || '重庆市区'
    const newCity = cityService.formatCityDisplay(newCityRaw)
    console.log('onShow - 新城市:', newCityRaw, '格式化:', newCity)
    
    // 如果城市发生变化，重新加载房源数据和统计数据（比较原始城市名称）
    if (oldCityRaw !== newCityRaw) {
      console.log('城市变化，重新加载数据:', oldCity, '=>', newCity)
      // 重新加载统计数据（使用原始城市名称）
      this.loadStatsData(newCityRaw);
      // 重新加载房源列表
      this.loadHouseList(true);
    }
    
    this.updateRegionOptions();
  },

  /**
   * 页面导航处理
   */
  navigateTo(e) {
    const page = e.currentTarget.dataset.page;
    console.log('导航到页面:', page);
    
    // 根据不同的页面类型进行导航
    switch(page) {
      case 'auction-house':
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=auction-house',
          success: () => {
            console.log('成功跳转到法拍住宅列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'auction-commercial':
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=auction-commercial',
          success: () => {
            console.log('成功跳转到法拍商办列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'special-assets':
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=special-assets',
          success: () => {
            console.log('成功跳转到特殊资产列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'premium-house':
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=premium-house',
          success: () => {
            console.log('成功跳转到精选房源列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'map':
        wx.showToast({
          title: '地图找房功能开发中',
          icon: 'none'
        });
        break;
      case 'buy-house':
        // 我要买房功能现在通过弹窗处理
        this.showBuyHouseModal();
        break;
      case 'sell-house':
        wx.navigateTo({
          url: '/pages/sell-house/sell-house',
          success: () => {
            console.log('成功跳转到卖房页面');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'live-stream':
        wx.showToast({
          title: '直播看房功能开发中',
          icon: 'none'
        });
        break;
      case 'service':
        wx.showToast({
          title: '服务流程功能开发中',
          icon: 'none'
        });
        break;
      case 'calculator':
        wx.navigateTo({
          url: '/pages/calculator/calculator'
        });
        break;
      case 'transfer':
        wx.showToast({
          title: '装修服务功能开发中',
          icon: 'none'
        });
        break;
      case 'cooperation':
        wx.navigateTo({
          url: '/pages/cooperation/cooperation',
          success: () => {
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      default:
        wx.showToast({
          title: '直播看房功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 搜索输入处理
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  /**
   * 搜索确认处理
   */
  onSearchConfirm(e) {
    const searchValue = e.detail.value.trim();
    if (searchValue) {
      console.log('搜索内容:', searchValue);

      // 显示加载提示
      util.showLoading('搜索中...');

      // 调用搜索API验证搜索结果
      houseService.searchHouses(searchValue)
        .then(response => {
          console.log('搜索结果:', response);
          util.hideLoading();

          if (response.code === 200 && Array.isArray(response.data)) {
            if (response.data.length > 0) {
              // 有搜索结果，跳转到房源列表页面显示结果
              wx.navigateTo({
                url: `/pages/house-list/house-list?search=${encodeURIComponent(searchValue)}`,
                success: () => {
                  console.log('跳转到搜索结果页面，搜索关键词:', searchValue);
                },
                fail: (err) => {
                  console.error('跳转失败:', err);
                  wx.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                  });
                }
              });
            } else {
              // 没有搜索结果
              wx.showToast({
                title: '未找到相关房源',
                icon: 'none',
                duration: 2000
              });
            }
          } else {
            wx.showToast({
              title: '搜索失败，请重试',
              icon: 'none'
            });
          }
        })
        .catch(err => {
          console.error('搜索失败:', err);
          util.hideLoading();

          // 搜索失败时仍然跳转到列表页面，让列表页面处理搜索
          wx.navigateTo({
            url: `/pages/house-list/house-list?search=${encodeURIComponent(searchValue)}`,
            success: () => {
              console.log('搜索API失败，跳转到搜索结果页面，搜索关键词:', searchValue);
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              wx.showToast({
                title: '搜索失败',
                icon: 'none'
              });
            }
          });
        });
    }
  },

  /**
   * 定位按钮点击处理
   */
  onLocationTap() {
    wx.getLocation({
      type: 'wgs84',
      success: (res) => {
        console.log('当前位置:', res);
        wx.showToast({
          title: '定位成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('定位失败:', err);
        wx.showToast({
          title: '定位失败，请检查权限',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 加载统计数据（支持按城市和时间筛选）
   */
  loadStatsData(city = null, createTimeFrom = null, createTimeTo = null) {
    util.showLoading('加载中...')

    // 如果没有传入城市，从存储中获取原始城市名称
    const currentCity = city || wx.getStorageSync('selectedCity') || '重庆市区'

    console.log('loadStatsData使用的城市:', currentCity)

    // 使用新的房源服务获取统计数据
    houseService.getHouseStats(currentCity, createTimeFrom, createTimeTo)
      .then(statsData => {
        console.log('统计数据获取成功:', statsData)
        console.log('avgPrice值:', statsData.avgPrice)
        this.setData({
          statsData: statsData
        })
        console.log('数据设置后的statsData:', this.data.statsData)
        util.hideLoading()
      })
      .catch(err => {
        console.error('加载统计数据失败:', err)

        // API失败时使用默认数据
        this.setData({
          statsData: {
            todayNew: Math.floor(Math.random() * 100) + 50,
            auctioning: Math.floor(Math.random() * 200) + 100,
            upcoming: Math.floor(Math.random() * 1000) + 2000,
            avgPrice: Math.floor(Math.random() * 2000000) + 7000000, // 默认700万-900万元
            totalCount: 0
          }
        })
        util.hideLoading()
      })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadStatsData();
    this.loadHouseList(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadHouseList();
    }
  },

  /**
   * 显示我要买房弹窗
   */
  showBuyHouseModal() {
    console.log('显示我要买房弹窗');
    this.setData({
      showBuyHouseModal: true
    });
  },

  /**
   * 隐藏我要买房弹窗
   */
  hideBuyHouseModal() {
    console.log('隐藏我要买房弹窗');
    this.setData({
      showBuyHouseModal: false
    });
  },

  /**
   * 阻止弹窗内容区域点击关闭
   */
  preventClose() {
    // 空方法，阻止事件冒泡
  },

  /**
   * 统计卡片点击事件（支持城市和时间筛选）
   */
  onStatsCardTap(e) {
    const type = e.currentTarget.dataset.type;
    let url = '/pages/house-list/house-list?';
    
    // 添加当前城市参数
    const currentCity = this.data.selectedCity;
    if (currentCity) {
      url += `city=${encodeURIComponent(currentCity)}&`;
    }

    // 获取当前时间
    const now = new Date();
    const nowStr = now.getFullYear() + '-' +
                   String(now.getMonth() + 1).padStart(2, '0') + '-' +
                   String(now.getDate()).padStart(2, '0') + ' ' +
                   String(now.getHours()).padStart(2, '0') + ':' +
                   String(now.getMinutes()).padStart(2, '0') + ':' +
                   String(now.getSeconds()).padStart(2, '0');

    // 获取今天的日期（用于今日新增）
    const todayStr = now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0');

    switch (type) {
      case 'todayNew':
        // 今日新增：调用今日新增API
        url += `statsType=todayNew&apiType=city-today`;
        break;
      case 'auctioning':
        // 正在拍卖：调用正在拍卖API
        url += `statsType=auctioning&apiType=city-ongoing`;
        break;
      case 'upcoming':
        // 即将拍卖：调用即将拍卖API
        url += `statsType=upcoming&apiType=city-upcoming`;
        break;
      case 'avgPrice':
        // 成交均价：不跳转，只显示信息
        wx.showToast({
          title: `当前成交均价：${this.data.statsData.avgPrice || 0}元`,
          icon: 'none',
          duration: 2000
        });
        return; // 不跳转
      default:
        url = '/pages/house-list/house-list';
        break;
    }

    console.log('统计卡片跳转:', type, url, '当前城市:', currentCity);
    wx.navigateTo({
      url: url
    });
  },

  /**
   * 手机号登录方式1
   */
  onPhoneLogin1() {
    console.log('选择手机号登录方式1');
    this.hideBuyHouseModal();
    wx.showToast({
      title: '手机号登录功能开发中',
      icon: 'none'
    });
  },

  /**
   * 手机号登录方式2
   */
  onPhoneLogin2() {
    console.log('选择手机号登录方式2');
    this.hideBuyHouseModal();
    wx.showToast({
      title: '手机号登录功能开发中',
      icon: 'none'
    });
  },

  /**
   * 统计卡片点击处理
   */
  onCard1Tap() {
    console.log('点击了卡片1');
    wx.showToast({
      title: '卡片1功能开发中',
      icon: 'none'
    });
  },

  onCard2Tap() {
    console.log('点击了卡片2');
    wx.showToast({
      title: '卡片2功能开发中',
      icon: 'none'
    });
  },

  onCard3Tap() {
    console.log('点击了卡片3');
    wx.showToast({
      title: '卡片3功能开发中',
      icon: 'none'
    });
  },

  onCard4Tap() {
    console.log('点击了卡片4');
    wx.showToast({
      title: '卡片4功能开发中',
      icon: 'none'
    });
  },

  /**
   * 加载轮播图数据
   */
  loadCarousels() {
    console.log('开始加载轮播图数据')

    carouselService.getCarousels()
      .then(carousels => {
        console.log('轮播图数据加载成功:', carousels)

        // 格式化轮播图数据
        const formattedCarousels = carouselService.formatCarousels(carousels)

        this.setData({
          carousels: formattedCarousels
        })

        // 预加载轮播图
        if (formattedCarousels.length > 0) {
          carouselService.preloadCarouselImages(formattedCarousels)
        }
      })
      .catch(error => {
        console.error('轮播图数据加载失败:', error)
        // 不显示错误提示，静默失败
      })
  },

  /**
   * 轮播图点击处理
   */
  onCarouselTap(e) {
    const carousel = e.currentTarget.dataset.carousel
    console.log('轮播图点击:', carousel)

    if (carousel) {
      carouselService.handleCarouselClick(carousel)
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '锦绣资产 - 创造资产价值',
      path: '/pages/index/index'
    };
  },

  /**
   * 初始化导航栏
   */
  initNavBar() {
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: systemInfo.statusBarHeight + 88
    });
  },

  /**
   * 加载选中的城市
   */
  loadSelectedCity() {
    const selectedCity = wx.getStorageSync('selectedCity') || '重庆市区';
    // 显示时使用格式化后的城市名称（添加"市"后缀）
    const displayCity = cityService.formatCityDisplay(selectedCity);
    this.setData({
      selectedCity: displayCity
    });
  },

  /**
   * 位置选择点击事件
   */
  onLocationSelect() {
    wx.navigateTo({
      url: '/pages/city-select/index',
      success: () => {
        console.log('跳转到城市选择页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 根据城市加载房源数据
   */
  loadHousesByCity() {
    const selectedCity = cityService.getCurrentCity()
    
    console.log('根据城市加载房源:', selectedCity)
    
    // 所有城市都使用统一的城市API
    return this.loadHousesByCityName(selectedCity)
  },

  /**
   * 根据区域列表加载房源
   */
  loadHousesByDistricts(districts) {
    const promises = districts.map(district => {
      return houseService.getHousesByDistrict(district)
        .catch(error => {
          console.warn(`获取${district}房源失败:`, error)
          return { code: 200, data: [] }
        })
    })

    return Promise.all(promises).then(responses => {
      let allHouses = []
      responses.forEach((response, index) => {
        if (response.code === 200 && Array.isArray(response.data)) {
          allHouses = allHouses.concat(response.data)
        }
      })
      return { code: 200, data: allHouses }
    })
  },

  /**
   * 根据城市加载房源
   */
  loadHousesByCityName(city) {
    return houseService.getHousesByCity(city)
  },

  /**
   * 根据筛选条件加载房源（支持区域筛选）
   */
  loadHousesWithFilters() {
    const selectedCity = cityService.getCurrentCity()
    const regionFilter = this.data.regionFilter
    
    console.log('根据筛选条件加载房源:', selectedCity, '区域筛选:', regionFilter)
    
    // 如果有区域筛选条件，根据区域查询
    if (regionFilter && regionFilter.length > 0) {
      return this.loadHousesBySelectedDistricts(regionFilter)
    } else {
      // 没有区域筛选，按城市查询
      return this.loadHousesByCity()
    }
  },

  /**
   * 根据选中的区域列表加载房源
   */
  loadHousesBySelectedDistricts(districts) {
    const promises = districts.map(district => {
      console.log('调用区域房源API:', district)
      return houseService.getHousesByDistrict(district)
        .catch(error => {
          console.warn(`获取${district}房源失败:`, error)
          return { code: 200, data: [] }
        })
    })

    return Promise.all(promises).then(responses => {
      let allHouses = []
      responses.forEach((response, index) => {
        if (response.code === 200 && Array.isArray(response.data)) {
          allHouses = allHouses.concat(response.data)
          console.log(`${districts[index]}区域房源数量:`, response.data.length)
        }
      })
      console.log('合并后的区域房源总数:', allHouses.length)
      return { code: 200, data: allHouses }
    })
  },

  /**
   * 加载房源列表（首页只显示住宅房源）
   */
  loadHouseList(refresh = false) {
    if (this.data.loading) return;

    this.setData({
      loading: true
    });

    if (refresh) {
      this.setData({
        currentPage: 1,
        houseList: [],
        hasMore: true
      });
    }

    // 根据当前选中的城市和区域筛选加载房源
    this.loadHousesWithFilters()
      .then(response => {
        console.log('首页房源列表API响应:', response)

        if (response.code === 200 && Array.isArray(response.data)) {
          // 首页只显示住宅房源（houseCategory = 0），先格式化数据
          let formattedHouseList = houseService.formatHouseList(response.data)
          
          // 过滤只显示住宅房源
          formattedHouseList = formattedHouseList.filter(house => house.houseCategory === 0)

          // 应用筛选条件
          formattedHouseList = this.applyFilters(formattedHouseList);

          // 应用排序
          formattedHouseList = this.applySorting(formattedHouseList);

          // 分页处理
          const startIndex = (this.data.currentPage - 1) * this.data.pageSize
          const endIndex = startIndex + this.data.pageSize
          const pageData = formattedHouseList.slice(startIndex, endIndex)

          let newHouseList = []
          if (this.data.currentPage === 1) {
            newHouseList = pageData
          } else {
            newHouseList = [...this.data.houseList, ...pageData]
          }

          this.setData({
            houseList: newHouseList,
            hasMore: endIndex < formattedHouseList.length,
            currentPage: this.data.currentPage + 1,
            loading: false
          })
        } else {
          console.warn('首页房源列表API返回数据格式异常:', response)
          this.handleAPIError()
        }
      })
      .catch(error => {
        console.error('首页房源列表API调用失败:', error)
        this.handleAPIError()
      })
  },

  /**
   * 调用房源列表API
   */
  callHouseListAPI(params) {
    // 根据房源类型选择不同的API
    let apiUrl = '';
    if (this.data.filterType === 'house' || this.data.filterType === 'all') {
      apiUrl = `${api.API.HOUSE_BY_TYPE}?houseType=住宅`;
    } else if (this.data.filterType === 'commercial') {
      apiUrl = `${api.API.HOUSE_BY_TYPE}?houseType=商办`;
    } else if (this.data.filterType === 'special') {
      apiUrl = `${api.API.HOUSE_BY_SPECIAL}/1`;
    } else if (this.data.filterType === 'premium') {
      apiUrl = `${api.API.HOUSE_BY_FEATURED}/1`;
    } else {
      // 默认获取所有房源
      apiUrl = api.API.HOUSE_LIST || `${api.API.HOUSE_BY_TYPE}?houseType=住宅`;
    }

    // 添加分页和筛选参数
    const queryParams = new URLSearchParams();
    queryParams.append('page', params.page);
    queryParams.append('pageSize', params.pageSize);

    if (params.region) queryParams.append('region', params.region);
    if (params.minPrice) queryParams.append('minPrice', params.minPrice);
    if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice);
    if (params.minArea) queryParams.append('minArea', params.minArea);
    if (params.maxArea) queryParams.append('maxArea', params.maxArea);
    if (params.auctionType) queryParams.append('auctionType', params.auctionType);
    if (params.auctionStatus) queryParams.append('auctionStatus', params.auctionStatus);
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const fullUrl = `${apiUrl}${apiUrl.includes('?') ? '&' : '?'}${queryParams.toString()}`;

    console.log('调用房源列表API:', fullUrl);

    api.get(fullUrl)
      .then(response => {
        console.log('房源列表API响应:', response);

        if (response.code === 200 && Array.isArray(response.data)) {
          const formattedHouseList = this.formatHouseListData(response.data);

          let newHouseList = [];
          if (this.data.currentPage === 1) {
            newHouseList = formattedHouseList;
          } else {
            newHouseList = [...this.data.houseList, ...formattedHouseList];
          }

          this.setData({
            houseList: newHouseList,
            hasMore: formattedHouseList.length >= this.data.pageSize,
            currentPage: this.data.currentPage + 1,
            loading: false
          });
        } else {
          console.warn('房源列表API返回数据格式异常:', response);
          this.handleAPIError();
        }
      })
      .catch(error => {
        console.error('房源列表API调用失败:', error);
        this.handleAPIError();
      });
  },

  /**
   * 处理API错误
   */
  handleAPIError() {
    // API失败时使用模拟数据
    const mockData = this.generateMockHouseData({
      page: this.data.currentPage,
      pageSize: this.data.pageSize
    });

    let newHouseList = [];
    if (this.data.currentPage === 1) {
      newHouseList = mockData.list;
    } else {
      newHouseList = [...this.data.houseList, ...mockData.list];
    }

    this.setData({
      houseList: newHouseList,
      hasMore: mockData.hasMore,
      currentPage: this.data.currentPage + 1,
      loading: false
    });

    wx.showToast({
      title: '网络异常，显示模拟数据',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 格式化房源列表数据
   */
  formatHouseListData(rawData) {
    return rawData.map(item => ({
      id: item.id || item.houseId,
      title: item.title || item.houseName || item.name || '房源信息',
      startingPrice: this.formatPrice(item.startingPrice || item.price || item.startPrice),
      area: item.area || item.buildingArea || item.houseArea || '未知',
      location: item.location || item.address || item.region || '位置未知',
      status: this.formatStatus(item.status || item.auctionStatus),
      auctionTime: this.formatAuctionTime(item.startTime || item.auctionTime),
      imageUrl: item.imageUrl || item.image || item.photo || '../../images/default-house.png',
      unitPrice: this.calculateUnitPrice(item.startingPrice || item.price, item.area || item.buildingArea),
      houseType: item.houseType || item.type || '住宅',
      auctionType: item.auctionType || '一拍',
      endTime: item.endTime,
      court: item.court || item.courtName,
      evaluationPrice: item.evaluationPrice || item.evalPrice
    }));
  },

  /**
   * 格式化价格
   */
  formatPrice(price) {
    if (!price) return '0';
    const numPrice = parseFloat(price);
    if (numPrice >= 10000) {
      return (numPrice / 10000).toFixed(0);
    }
    return numPrice.toFixed(0);
  },

  /**
   * 格式化状态
   */
  formatStatus(status) {
    const statusMap = {
      '0': '未起拍',
      '1': '竞拍中',
      '2': '已成交',
      '3': '已结束',
      'pending': '未起拍',
      'ongoing': '竞拍中',
      'completed': '已成交',
      'ended': '已结束'
    };
    return statusMap[status] || status || '拍卖中';
  },

  /**
   * 格式化拍卖时间
   */
  formatAuctionTime(timeStr) {
    if (!timeStr) return '时间待定';
    try {
      const date = new Date(timeStr);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hour = date.getHours().toString().padStart(2, '0');
      const minute = date.getMinutes().toString().padStart(2, '0');
      return `${month}-${day} ${hour}:${minute}`;
    } catch (error) {
      return timeStr;
    }
  },

  /**
   * 计算单价
   */
  calculateUnitPrice(totalPrice, area) {
    if (!totalPrice || !area) return 0;
    const price = parseFloat(totalPrice);
    const areaNum = parseFloat(area);
    if (areaNum > 0) {
      return Math.round((price * 10000) / areaNum);
    }
    return 0;
  },

  /**
   * 从筛选条件获取房源类型
   */
  getHouseTypeFromFilter() {
    const typeMap = {
      'all': '',
      'house': '住宅',
      'commercial': '商办',
      'special': '特殊资产',
      'premium': '精选房源'
    };
    return typeMap[this.data.filterType] || '';
  },

  /**
   * 从价格筛选获取最低价格
   */
  getMinPriceFromFilter() {
    const priceFilter = this.data.priceFilter;
    if (priceFilter === 'all') return '';

    if (priceFilter.startsWith('custom-')) {
      const parts = priceFilter.split('-');
      return parts[1] || '';
    }

    const priceMap = {
      '0-50': '0',
      '50-100': '50',
      '100-150': '100',
      '150-200': '150',
      '200-300': '200',
      '300-400': '300',
      '400-500': '400',
      '500-700': '500',
      '700-1000': '700',
      '1000+': '1000'
    };
    return priceMap[priceFilter] || '';
  },

  /**
   * 从价格筛选获取最高价格
   */
  getMaxPriceFromFilter() {
    const priceFilter = this.data.priceFilter;
    if (priceFilter === 'all') return '';

    if (priceFilter.startsWith('custom-')) {
      const parts = priceFilter.split('-');
      return parts[2] || '';
    }

    const priceMap = {
      '0-50': '50',
      '50-100': '100',
      '100-150': '150',
      '150-200': '200',
      '200-300': '300',
      '300-400': '400',
      '400-500': '500',
      '500-700': '700',
      '700-1000': '1000',
      '1000+': ''
    };
    return priceMap[priceFilter] || '';
  },

  /**
   * 从面积筛选获取最小面积
   */
  getMinAreaFromFilter() {
    const areaFilter = this.data.areaFilter;
    if (areaFilter === 'all') return '';

    const areaMap = {
      '0-50': '0',
      '50-70': '50',
      '70-90': '70',
      '90-120': '90',
      '120-150': '120',
      '150-200': '150',
      '200-300': '200',
      '300+': '300'
    };
    return areaMap[areaFilter] || '';
  },

  /**
   * 从面积筛选获取最大面积
   */
  getMaxAreaFromFilter() {
    const areaFilter = this.data.areaFilter;
    if (areaFilter === 'all') return '';

    const areaMap = {
      '0-50': '50',
      '50-70': '70',
      '70-90': '90',
      '90-120': '120',
      '120-150': '150',
      '150-200': '200',
      '200-300': '300',
      '300+': ''
    };
    return areaMap[areaFilter] || '';
  },

  /**
   * 从排序筛选获取排序字段
   */
  getSortByFromFilter() {
    const sortType = this.data.sortType;
    const sortMap = {
      'smart': '',
      'latest': 'createTime',
      'price-asc': 'startingPrice',
      'price-desc': 'startingPrice',
      'unit-price-asc': 'unitPrice',
      'unit-price-desc': 'unitPrice',
      'area-desc': 'area',
      'area-asc': 'area',
      'time-asc': 'startTime'
    };
    return sortMap[sortType] || '';
  },

  /**
   * 从排序筛选获取排序顺序
   */
  getSortOrderFromFilter() {
    const sortType = this.data.sortType;
    const orderMap = {
      'smart': '',
      'latest': 'desc',
      'price-asc': 'asc',
      'price-desc': 'desc',
      'unit-price-asc': 'asc',
      'unit-price-desc': 'desc',
      'area-desc': 'desc',
      'area-asc': 'asc',
      'time-asc': 'asc'
    };
    return orderMap[sortType] || '';
  },

  /**
   * 房源类型筛选点击事件 - 跳转到房源列表页
   */
  onHouseTypeFilter(e) {
    const type = e.currentTarget.dataset.type;
    console.log('房源类型筛选点击:', type);

    // 构建跳转参数
    const params = {
      filterType: type
    };

    // 跳转到房源列表页面
    wx.navigateTo({
      url: `/pages/house-list/house-list?filterType=${type}`,
      success: () => {
        console.log('成功跳转到房源列表页面，筛选类型:', type);
      },
      fail: (err) => {
        console.error('跳转房源列表页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 统计卡片点击事件 - 跳转到房源列表页
   */
  onStatsCardTap(e) {
    const type = e.currentTarget.dataset.type;
    console.log('统计卡片点击:', type);

    let url = '';
    let title = '';

    switch (type) {
      case 'todayNew':
        // 今日新增：根据createTime筛选今天创建的房源
        url = `/pages/house-list/house-list?statsType=todayNew`;
        title = '今日新增房源';
        break;
      case 'auctioning':
        // 正在拍卖：auctionStatus为1或2
        url = `/pages/house-list/house-list?statsType=auctioning`;
        title = '正在拍卖房源';
        break;
      case 'upcoming':
        // 即将拍卖：auctionStatus为0
        url = `/pages/house-list/house-list?statsType=upcoming`;
        title = '即将拍卖房源';
        break;
      default:
        console.log('未知的统计类型:', type);
        return;
    }

    // 跳转到房源列表页面
    wx.navigateTo({
      url: url,
      success: () => {
        console.log('成功跳转到房源列表页面，统计类型:', type);
      },
      fail: (err) => {
        console.error('跳转房源列表页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 生成模拟房源数据
   */
  generateMockHouseData(params) {
    const mockHouses = [];
    const titles = [
      '万科城市花园三室两厅',
      '恒大绿洲精装修住宅',
      '保利花园景观房',
      '龙湖春森彼岸',
      '融创玖玺台',
      '华润二十四城',
      '金科天籁城',
      '中海寰宇天下'
    ];

    const locations = ['渝中区', '江北区', '南岸区', '沙坪坝区', '九龙坡区', '大渡口区'];
    const statuses = ['拍卖中', '即将开拍', '已结束'];

    for (let i = 0; i < params.pageSize; i++) {
      const id = (params.page - 1) * params.pageSize + i + 1;
      mockHouses.push({
        id: id,
        title: titles[Math.floor(Math.random() * titles.length)],
        startingPrice: (Math.random() * 200 + 50).toFixed(0),
        area: (Math.random() * 100 + 50).toFixed(0),
        location: locations[Math.floor(Math.random() * locations.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        auctionTime: '2024-12-' + (Math.floor(Math.random() * 28) + 1).toString().padStart(2, '0'),
        imageUrl: '../../images/default-house.png'
      });
    }

    return {
      list: mockHouses,
      hasMore: params.page < 5 // 模拟5页数据
    };
  },

  /**
   * 更新区域选项
   */
  async updateRegionOptions() {
    const selectedCity = this.data.selectedCity;
    let regionOptions = [];

    // 所有城市都调用API获取区域
    try {
      const response = await houseService.getDistrictsByCity(selectedCity);
      console.log('获取到的区域数据:', response);
      
      if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
        regionOptions = response.data;
      } else {
        // 如果API没有返回数据，使用默认区域选项
        regionOptions = ['市中心', '新区', '开发区', '高新区'];
      }
    } catch (error) {
      console.error('获取区域数据失败:', error);
      // 降级到默认区域选项
      regionOptions = ['市中心', '新区', '开发区', '高新区'];
    }

    this.setData({
      currentRegionOptions: regionOptions
    });
  },

  /**
   * 显示区域筛选面板
   */
  showRegionFilter() {
    this.setData({
      showRegionPanel: true,
      tempRegionFilter: [...this.data.regionFilter]
    });
    // 初始化选中状态
    this.updateRegionActiveStatus();
  },

  /**
   * 隐藏区域筛选面板
   */
  hideRegionFilter() {
    this.setData({
      showRegionPanel: false
    });
  },

  /**
   * 临时区域筛选点击事件（支持多选）
   */
  onTempRegionFilterTap(e) {
    const region = e.currentTarget.dataset.region;
    let tempRegionFilter = [...this.data.tempRegionFilter];

    if (region === 'all') {
      // 点击"不限"，清空所有选择
      tempRegionFilter = [];
    } else {
      // 切换选择状态
      const index = tempRegionFilter.indexOf(region);
      if (index > -1) {
        // 已选中，取消选择
        tempRegionFilter.splice(index, 1);
      } else {
        // 未选中，添加选择
        tempRegionFilter.push(region);
      }
    }

    this.setData({
      tempRegionFilter: tempRegionFilter
    });
    // 更新选中状态
    this.updateRegionActiveStatus();
  },

  /**
   * 更新区域选中状态
   */
  updateRegionActiveStatus() {
    const activeStatus = {};
    this.data.currentRegionOptions.forEach(region => {
      activeStatus[region] = this.data.tempRegionFilter.includes(region);
    });
    this.setData({
      regionActiveStatus: activeStatus
    });
  },

  /**
   * 重置区域筛选
   */
  resetRegionFilter() {
    this.setData({
      tempRegionFilter: []
    });
    this.updateRegionActiveStatus();
  },

  /**
   * 确认区域筛选
   */
  confirmRegionFilter() {
    let regionText = '区域';
    if (this.data.tempRegionFilter.length > 0) {
      if (this.data.tempRegionFilter.length === 1) {
        regionText = this.data.tempRegionFilter[0];
      } else {
        regionText = `${this.data.tempRegionFilter.length}项`;
      }
    }

    this.setData({
      regionFilter: [...this.data.tempRegionFilter],
      regionFilterText: regionText,
      showRegionPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 显示面积筛选面板
   */
  showAreaFilter() {
    this.setData({
      showAreaPanel: true,
      tempAreaFilter: [...this.data.areaFilter]
    });
    this.updateAreaActiveStatus();
  },

  /**
   * 隐藏面积筛选面板
   */
  hideAreaFilter() {
    this.setData({
      showAreaPanel: false
    });
  },

  /**
   * 临时面积筛选点击事件（支持多选）
   */
  onTempAreaFilterTap(e) {
    const area = e.currentTarget.dataset.area;
    let tempAreaFilter = [...this.data.tempAreaFilter];

    if (area === 'all') {
      // 点击"不限"，清空所有选择
      tempAreaFilter = [];
    } else {
      // 切换选择状态
      const index = tempAreaFilter.indexOf(area);
      if (index > -1) {
        // 已选中，取消选择
        tempAreaFilter.splice(index, 1);
      } else {
        // 未选中，添加选择
        tempAreaFilter.push(area);
      }
    }

    this.setData({
      tempAreaFilter: tempAreaFilter
    });
    this.updateAreaActiveStatus();
  },

  /**
   * 更新面积选中状态
   */
  updateAreaActiveStatus() {
    const areaOptions = ['0-50', '50-70', '70-90', '90-120', '120-150', '150-200', '200-300', '300+'];
    const activeStatus = {};
    areaOptions.forEach(area => {
      activeStatus[area] = this.data.tempAreaFilter.includes(area);
    });
    this.setData({
      areaActiveStatus: activeStatus
    });
  },

  /**
   * 重置面积筛选
   */
  resetAreaFilter() {
    this.setData({
      tempAreaFilter: []
    });
    this.updateAreaActiveStatus();
  },

  /**
   * 确认面积筛选
   */
  confirmAreaFilter() {
    let areaText = '面积';
    if (this.data.tempAreaFilter.length > 0) {
      if (this.data.tempAreaFilter.length === 1) {
        areaText = this.getAreaText(this.data.tempAreaFilter[0]);
      } else {
        areaText = `${this.data.tempAreaFilter.length}项`;
      }
    }

    this.setData({
      areaFilter: [...this.data.tempAreaFilter],
      areaFilterText: areaText,
      showAreaPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取面积文本
   */
  getAreaText(areaFilter) {
    const areaMap = {
      '0-50': '50㎡以下',
      '50-70': '50~70㎡',
      '70-90': '70~90㎡',
      '90-120': '90~120㎡',
      '120-150': '120~150㎡',
      '150-200': '150~200㎡',
      '200-300': '200~300㎡',
      '300+': '300㎡以上'
    };
    return areaMap[areaFilter] || '面积';
  },

  /**
   * 显示价格筛选面板
   */
  showPriceFilter() {
    this.setData({
      showPricePanel: true,
      tempPriceFilter: [...this.data.priceFilter]
    });
    this.updatePriceActiveStatus();
  },

  /**
   * 隐藏价格筛选面板
   */
  hidePriceFilter() {
    this.setData({
      showPricePanel: false
    });
  },

  /**
   * 临时价格筛选点击事件（支持多选）
   */
  onTempPriceFilterTap(e) {
    const price = e.currentTarget.dataset.price;
    let tempPriceFilter = [...this.data.tempPriceFilter];

    if (price === 'all') {
      // 点击"不限"，清空所有选择
      tempPriceFilter = [];
    } else {
      // 切换选择状态
      const index = tempPriceFilter.indexOf(price);
      if (index > -1) {
        // 已选中，取消选择
        tempPriceFilter.splice(index, 1);
      } else {
        // 未选中，添加选择
        tempPriceFilter.push(price);
      }
    }

    this.setData({
      tempPriceFilter: tempPriceFilter
    });
    this.updatePriceActiveStatus();
  },

  /**
   * 更新价格选中状态
   */
  updatePriceActiveStatus() {
    const priceOptions = ['0-50', '50-100', '100-150', '150-200', '200-300', '300-400', '400-500', '500-700', '700-1000', '1000+'];
    const activeStatus = {};
    priceOptions.forEach(price => {
      activeStatus[price] = this.data.tempPriceFilter.includes(price);
    });
    this.setData({
      priceActiveStatus: activeStatus
    });
  },

  /**
   * 最低价格输入
   */
  onMinPriceInput(e) {
    this.setData({
      customMinPrice: e.detail.value
    });
  },

  /**
   * 最高价格输入
   */
  onMaxPriceInput(e) {
    this.setData({
      customMaxPrice: e.detail.value
    });
  },

  /**
   * 重置价格筛选
   */
  resetPriceFilter() {
    this.setData({
      tempPriceFilter: [],
      customMinPrice: '',
      customMaxPrice: ''
    });
    this.updatePriceActiveStatus();
  },

  /**
   * 确认价格筛选
   */
  confirmPriceFilter() {
    let priceText = '价格';
    let priceFilter = [...this.data.tempPriceFilter];

    // 如果有自定义价格，优先使用自定义价格
    if (this.data.customMinPrice || this.data.customMaxPrice) {
      const minPrice = this.data.customMinPrice || '0';
      const maxPrice = this.data.customMaxPrice || '不限';
      priceText = `${minPrice}-${maxPrice}万`;
      priceFilter = [`custom-${minPrice}-${maxPrice}`];
    } else if (priceFilter.length > 0) {
      if (priceFilter.length === 1) {
        priceText = this.getPriceText(priceFilter[0]);
      } else {
        priceText = `${priceFilter.length}项`;
      }
    }

    this.setData({
      priceFilter: priceFilter,
      priceFilterText: priceText,
      showPricePanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取价格文本
   */
  getPriceText(priceFilter) {
    const priceMap = {
      '0-50': '50万以下',
      '50-100': '50~100万',
      '100-150': '100~150万',
      '150-200': '150~200万',
      '200-300': '200~300万',
      '300-400': '300~400万',
      '400-500': '400~500万',
      '500-700': '500~700万',
      '700-1000': '700~1000万',
      '1000+': '1000万以上'
    };
    return priceMap[priceFilter] || '价格';
  },

  /**
   * 显示更多筛选面板
   */
  showMoreFilter() {
    this.setData({
      showMorePanel: true,
      tempAuctionType: [...this.data.auctionType],
      tempAuctionStatus: [...this.data.auctionStatus]
    });
    this.updateAuctionActiveStatus();
  },

  /**
   * 隐藏更多筛选面板
   */
  hideMoreFilter() {
    this.setData({
      showMorePanel: false
    });
  },

  /**
   * 临时拍卖方式筛选点击事件（支持多选）
   */
  onTempAuctionTypeTap(e) {
    const type = e.currentTarget.dataset.type;
    let tempAuctionType = [...this.data.tempAuctionType];

    if (type === 'all') {
      // 点击"不限"，清空所有选择
      tempAuctionType = [];
    } else {
      // 切换选择状态
      const index = tempAuctionType.indexOf(type);
      if (index > -1) {
        // 已选中，取消选择
        tempAuctionType.splice(index, 1);
      } else {
        // 未选中，添加选择
        tempAuctionType.push(type);
      }
    }

    this.setData({
      tempAuctionType: tempAuctionType
    });
    this.updateAuctionActiveStatus();
  },

  /**
   * 临时拍卖状态筛选点击事件（支持多选）
   */
  onTempAuctionStatusTap(e) {
    const status = e.currentTarget.dataset.status;
    let tempAuctionStatus = [...this.data.tempAuctionStatus];

    if (status === 'all') {
      // 点击"不限"，清空所有选择
      tempAuctionStatus = [];
    } else {
      // 切换选择状态
      const index = tempAuctionStatus.indexOf(status);
      if (index > -1) {
        // 已选中，取消选择
        tempAuctionStatus.splice(index, 1);
      } else {
        // 未选中，添加选择
        tempAuctionStatus.push(status);
      }
    }

    this.setData({
      tempAuctionStatus: tempAuctionStatus
    });
    this.updateAuctionActiveStatus();
  },

  /**
   * 更新拍卖选中状态
   */
  updateAuctionActiveStatus() {
    const auctionTypeOptions = ['一拍', '二拍', '变卖', '其他'];
    const auctionStatusOptions = ['未起拍', '竞拍中', '已成交', '已结束'];

    const typeActiveStatus = {};
    auctionTypeOptions.forEach(type => {
      typeActiveStatus[type] = this.data.tempAuctionType.includes(type);
    });

    const statusActiveStatus = {};
    auctionStatusOptions.forEach(status => {
      statusActiveStatus[status] = this.data.tempAuctionStatus.includes(status);
    });

    this.setData({
      auctionTypeActiveStatus: typeActiveStatus,
      auctionStatusActiveStatus: statusActiveStatus
    });
  },

  /**
   * 重置更多筛选
   */
  resetMoreFilter() {
    this.setData({
      tempAuctionType: [],
      tempAuctionStatus: []
    });
    this.updateAuctionActiveStatus();
  },

  /**
   * 确认更多筛选
   */
  confirmMoreFilter() {
    this.setData({
      auctionType: [...this.data.tempAuctionType],
      auctionStatus: [...this.data.tempAuctionStatus],
      showMorePanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 显示排序筛选面板
   */
  showSortFilter() {
    this.setData({
      showSortPanel: true,
      tempSortType: this.data.sortType
    });
  },

  /**
   * 隐藏排序筛选面板
   */
  hideSortFilter() {
    this.setData({
      showSortPanel: false
    });
  },

  /**
   * 临时排序类型点击事件
   */
  onTempSortTypeTap(e) {
    const sort = e.currentTarget.dataset.sort;
    this.setData({
      tempSortType: sort
    });
  },

  /**
   * 重置排序筛选
   */
  resetSortFilter() {
    this.setData({
      tempSortType: 'smart'
    });
  },

  /**
   * 确认排序筛选
   */
  confirmSortFilter() {
    const sortText = this.getSortText(this.data.tempSortType);
    this.setData({
      sortType: this.data.tempSortType,
      sortFilterText: sortText,
      showSortPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取排序文本
   */
  getSortText(sortType) {
    const sortMap = {
      'smart': '智能排序',
      'latest': '最新发布',
      'price-asc': '总价从低到高',
      'price-desc': '总价从高到低',
      'unit-price-asc': '单价从低到高',
      'unit-price-desc': '单价从高到低',
      'area-desc': '面积从大到小',
      'area-asc': '面积从小到大',
      'time-asc': '起拍时间由近到远'
    };
    return sortMap[sortType] || '智能排序';
  },

  /**
   * 房源项点击事件
   */
  onHouseItemTap(e) {
    const house = e.currentTarget.dataset.house;
    wx.navigateTo({
      url: `/pages/property-detail/property-detail?id=${house.id}`,
      success: () => {
        console.log('跳转到房源详情页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 切换收藏状态
   */
  toggleFavorite(e) {
    const id = e.currentTarget.dataset.id;
    const houseList = this.data.houseList.map(house => {
      if (house.id === id) {
        return { ...house, isFavorite: !house.isFavorite };
      }
      return house;
    });

    this.setData({
      houseList: houseList
    });

    // 显示提示
    const house = houseList.find(h => h.id === id);
    wx.showToast({
      title: house.isFavorite ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1500
    });
  },

  // 应用筛选条件
  applyFilters: function(houses) {
    let filteredHouses = houses;

    // 区域筛选 - 从标题中获取
    if (this.data.regionFilter.length > 0) {
      filteredHouses = filteredHouses.filter(house => {
        const title = house.title || '';
        return this.data.regionFilter.some(region => title.includes(region));
      });
    }

    // 面积筛选 - 从建筑面积字段获取
    if (this.data.areaFilter.length > 0) {
      filteredHouses = filteredHouses.filter(house => {
        const area = parseFloat(house.buildingArea) || 0;
        return this.data.areaFilter.some(areaRange => {
          return this.isAreaInRange(area, areaRange);
        });
      });
    }

    // 价格筛选 - 从起拍价获取
    if (this.data.priceFilter.length > 0) {
      filteredHouses = filteredHouses.filter(house => {
        const startPrice = parseFloat(house.startPrice) || 0;
        const priceInWan = startPrice / 10000; // 转换为万元
        return this.data.priceFilter.some(priceRange => {
          return this.isPriceInRange(priceInWan, priceRange);
        });
      });
    }

    // 拍卖方式筛选 - 从标题中获取
    if (this.data.auctionType.length > 0) {
      filteredHouses = filteredHouses.filter(house => {
        const title = house.title || '';
        return this.data.auctionType.some(type => title.includes(type));
      });
    }

    // 拍卖状态筛选 - 从时间判断
    if (this.data.auctionStatus.length > 0) {
      const currentTime = new Date();
      filteredHouses = filteredHouses.filter(house => {
        const status = this.getAuctionStatus(house, currentTime);
        return this.data.auctionStatus.includes(status);
      });
    }

    return filteredHouses;
  },

  // 判断面积是否在范围内
  isAreaInRange: function(area, areaRange) {
    switch (areaRange) {
      case '0-50': return area < 50;
      case '50-70': return area >= 50 && area < 70;
      case '70-90': return area >= 70 && area < 90;
      case '90-120': return area >= 90 && area < 120;
      case '120-150': return area >= 120 && area < 150;
      case '150-200': return area >= 150 && area < 200;
      case '200-300': return area >= 200 && area < 300;
      case '300+': return area >= 300;
      default: return false;
    }
  },

  // 判断价格是否在范围内
  isPriceInRange: function(price, priceRange) {
    switch (priceRange) {
      case '0-50': return price < 50;
      case '50-100': return price >= 50 && price < 100;
      case '100-150': return price >= 100 && price < 150;
      case '150-200': return price >= 150 && price < 200;
      case '200-300': return price >= 200 && price < 300;
      case '300-400': return price >= 300 && price < 400;
      case '400-500': return price >= 400 && price < 500;
      case '500-700': return price >= 500 && price < 700;
      case '700-1000': return price >= 700 && price < 1000;
      case '1000+': return price >= 1000;
      default: return false;
    }
  },

  // 获取拍卖状态
  getAuctionStatus: function(house, currentTime) {
    // 尝试多个可能的时间字段名
    let startTimeValue = house.auctionStartTime || house.startTime || house.beginTime || house.auctionTime;
    let endTimeValue = house.auctionEndTime || house.endTime || house.finishTime;

    if (!startTimeValue) return '未起拍';

    // 处理时间格式
    let startTime;
    if (startTimeValue.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
      const currentYear = new Date().getFullYear();
      startTime = new Date(`${currentYear}-${startTimeValue}`);
    } else {
      startTime = new Date(startTimeValue);
    }

    let endTime;
    if (endTimeValue) {
      if (endTimeValue.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
        const currentYear = new Date().getFullYear();
        endTime = new Date(`${currentYear}-${endTimeValue}`);
      } else {
        endTime = new Date(endTimeValue);
      }
    }

    // 判断状态
    if (currentTime < startTime) {
      return '未起拍';
    } else if (endTime && currentTime > endTime) {
      return '已结束';
    } else {
      return '竞拍中';
    }
  },

  // 应用排序
  applySorting: function(houses) {
    const sortType = this.data.sortType;

    return houses.sort((a, b) => {
      switch (sortType) {
        case 'price-asc':
          return (parseFloat(a.startPrice) || 0) - (parseFloat(b.startPrice) || 0);
        case 'price-desc':
          return (parseFloat(b.startPrice) || 0) - (parseFloat(a.startPrice) || 0);
        case 'unit-price-asc':
          const unitPriceA = (parseFloat(a.startPrice) || 0) / (parseFloat(a.buildingArea) || 1);
          const unitPriceB = (parseFloat(b.startPrice) || 0) / (parseFloat(b.buildingArea) || 1);
          return unitPriceA - unitPriceB;
        case 'unit-price-desc':
          const unitPriceA2 = (parseFloat(a.startPrice) || 0) / (parseFloat(a.buildingArea) || 1);
          const unitPriceB2 = (parseFloat(b.startPrice) || 0) / (parseFloat(b.buildingArea) || 1);
          return unitPriceB2 - unitPriceA2;
        case 'area-desc':
          return (parseFloat(b.buildingArea) || 0) - (parseFloat(a.buildingArea) || 0);
        case 'area-asc':
          return (parseFloat(a.buildingArea) || 0) - (parseFloat(b.buildingArea) || 0);
        case 'latest':
          // 最新发布：按创建时间降序排序
          const createTimeA = new Date(a.createTime || a.publishTime || 0);
          const createTimeB = new Date(b.createTime || b.publishTime || 0);
          return createTimeB - createTimeA;
        case 'time-asc':
          // 按起拍时间排序
          const timeA = this.getAuctionStartTime(a);
          const timeB = this.getAuctionStartTime(b);
          return timeA - timeB;
        case 'smart':
        default:
          // 智能排序：综合考虑时间和价格
          return 0;
      }
    });
  },

  // 获取拍卖开始时间
  getAuctionStartTime: function(house) {
    let startTimeValue = house.auctionStartTime || house.startTime || house.beginTime || house.auctionTime;
    if (!startTimeValue) return new Date(0);

    if (startTimeValue.match(/^\d{2}-\d{2} \d{2}:\d{2}$/)) {
      const currentYear = new Date().getFullYear();
      return new Date(`${currentYear}-${startTimeValue}`);
    } else {
      return new Date(startTimeValue);
    }
  }
});
